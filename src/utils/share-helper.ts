import Taro from '@tarojs/taro'

/**
 * 微信小程序分享功能辅助工具
 * 符合微信小程序官方规范，避免违规分享
 */
export class ShareHelper {
  // 默认分享配置
  private static defaultConfig = {
    title: '暴风回收 - 专业手机回收平台',
    path: '/pages/index/index',
    imageUrl: ''
  }

  /**
   * 获取分享配置
   * @param options 自定义分享选项
   * @returns 分享配置对象
   */
  static getShareConfig(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    return {
      ...this.defaultConfig,
      ...options
    }
  }

  /**
   * 显示分享菜单（符合微信规范）
   * @param withShareTicket 是否使用带 shareTicket 的转发
   */
  static showShareMenu(withShareTicket: boolean = true) {
    if (process.env.TARO_ENV === 'weapp') {
      Taro.showShareMenu({
        withShareTicket
      })
    }
  }

  /**
   * 隐藏分享菜单
   */
  static hideShareMenu() {
    if (process.env.TARO_ENV === 'weapp') {
      Taro.hideShareMenu()
    }
  }

  /**
   * 引导用户分享（不直接调用分享，符合微信规范）
   * @param message 提示消息
   */
  static guideUserToShare(message: string = '请点击右上角分享按钮') {
    Taro.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    })
  }

  /**
   * 复制链接到剪贴板
   * @param link 要复制的链接
   */
  static async copyLink(link: string) {
    try {
      await Taro.setClipboardData({
        data: link
      })
      Taro.showToast({
        title: '链接已复制',
        icon: 'success',
        duration: 2000
      })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000
      })
    }
  }

  /**
   * 显示分享选项（不直接调用分享API）
   * @param callback 选择回调
   */
  static showShareOptions(callback?: (type: 'share' | 'copy') => void) {
    Taro.showActionSheet({
      itemList: ['分享小程序', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            callback?.('share')
            break
          case 1:
            callback?.('copy')
            break
        }
      }
    })
  }

  /**
   * 一键分享功能（符合规范）
   * @param options 分享选项
   */
  static oneClickShare(options?: {
    title?: string
    path?: string
    imageUrl?: string
    copyLink?: string
  }) {
    this.showShareOptions((type) => {
      switch (type) {
        case 'share':
          // 显示分享菜单并引导用户
          this.showShareMenu(true)
          this.guideUserToShare('请点击右上角分享给好友')
          break
        case 'copy':
          // 复制链接
          const link = options?.copyLink || 'https://www.baofengrecycle.com'
          this.copyLink(link)
          break
      }
    })
  }

  /**
   * 设置页面分享配置的辅助方法
   * @param options 分享选项
   * @returns 分享配置对象
   */
  static setupPageShare(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const config = this.getShareConfig(options)
    
    return {
      // 分享给好友的配置
      shareAppMessage: () => config,
      // 分享到朋友圈的配置
      shareTimeline: () => ({
        title: config.title,
        imageUrl: config.imageUrl
      })
    }
  }
}

export default ShareHelper
