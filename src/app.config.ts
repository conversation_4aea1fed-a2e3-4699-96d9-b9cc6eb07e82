export default {
  pages: [
    'pages/index/index',
    'pages/category/index',
    'pages/search/index',
    'pages/ranking/index',
    'pages/profile/index',
    'pages/phone-detail/index',
    'pages/test/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '暴风回收',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#1890ff',
    backgroundColor: '#ffffff',
    borderStyle: 'black',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: '/assets/icons/home.png',
        selectedIconPath: '/assets/icons/home-active.png'
      },
      {
        pagePath: 'pages/category/index',
        text: '类目',
        iconPath: '/assets/icons/category.png',
        selectedIconPath: '/assets/icons/category-active.png'
      },
      {
        pagePath: 'pages/ranking/index',
        text: '涨跌榜',
        iconPath: '/assets/icons/ranking.png',
        selectedIconPath: '/assets/icons/ranking-active.png'
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
        iconPath: '/assets/icons/profile.png',
        selectedIconPath: '/assets/icons/profile-active.png'
      }
    ]
  },
  // 小程序接口权限相关设置
  requiredBackgroundModes: [],
  // 分包配置（如果需要）
  subpackages: [],
  // 网络超时时间
  networkTimeout: {
    request: 10000,
    downloadFile: 10000
  },
  // 调试模式
  debug: true
}
