import { useEffect } from 'react'
import { useDidShow, useDidHide } from '@tarojs/taro'
import { ConfigProvider } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import UserService from './services/user'
// 全局样式
import './app.scss'

function App(props: any) {
  // 可以使用所有的 React Hooks
  useEffect(() => {})

  // 对应 onShow
  useDidShow(() => {})

  // 对应 onHide
  useDidHide(() => {})

  // NutUI 主题配置
  const customTheme = {
    nutuiCellGroupBackgroundColor: '#f3f3f3'
  }

  return (
    <ConfigProvider theme={customTheme}>
      {props.children}
    </ConfigProvider>
  )
}

export default App
