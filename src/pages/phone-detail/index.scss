.phone-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .header {
    background: white;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    z-index: 100;

    .back-btn {
      font-size: 18px;
      color: #333;
      padding: 5px;
    }

    .header-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .header-actions {
      display: flex;
      gap: 10px;

      .more-btn,
      .minimize-btn,
      .close-btn {
        font-size: 16px;
        color: #666;
        padding: 2px 5px;
      }
    }
  }

  .content {
    flex: 1;
    padding: 0 15px 100px;
  }

  .phone-title {
    background: white;
    margin: 15px 0;
    padding: 20px 15px;
    border-radius: 8px;

    .phone-name {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
  }

  .selection-section {
    background: white;
    margin: 15px 0;
    padding: 15px;
    border-radius: 8px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .section-icon {
        font-size: 16px;
        margin-right: 8px;
      }

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .options-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .option-item {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 20px;
        padding: 8px 16px;
        min-width: 80px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &.selected {
          background: #e3f2fd;
          border-color: #2196f3;
          color: #1976d2;
        }

        .option-text {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .notice-section {
    margin: 15px 0;
    padding: 12px 15px;
    background: #fff3e0;
    border-radius: 8px;
    border-left: 4px solid #ff9800;

    .notice-text {
      font-size: 14px;
      color: #e65100;
      line-height: 1.4;
    }
  }

  .price-section {
    background: white;
    margin: 15px 0;
    padding: 15px;
    border-radius: 8px;

    .price-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }

    .price-notice {
      background: #f0f0f0;
      padding: 12px;
      border-radius: 8px;
      display: flex;
      align-items: flex-start;

      .notice-icon {
        font-size: 16px;
        margin-right: 8px;
        margin-top: 2px;
      }

      .notice-content {
        flex: 1;
        font-size: 12px;
        color: #666;
        line-height: 1.4;
      }
    }
  }

  .condition-section {
    margin: 15px 0;

    .condition-item {
      background: white;
      margin-bottom: 15px;
      padding: 15px;
      border-radius: 8px;

      .condition-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .condition-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .condition-tags {
          display: flex;
          gap: 5px;

          .tag {
            background: #e8f5e8;
            color: #4caf50;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            border: 1px solid #c8e6c9;
          }
        }
      }

      .condition-content {
        display: flex;
        align-items: flex-start;
        gap: 15px;

        .condition-price {
          font-size: 20px;
          font-weight: 600;
          color: #f44336;
          min-width: 80px;

          &::before {
            content: '¥';
            font-size: 16px;
          }
        }

        .condition-desc {
          flex: 1;
          font-size: 13px;
          color: #666;
          line-height: 1.4;
        }
      }
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 15px;
    border-top: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 100;

    .back-to-top {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 5px;
      cursor: pointer;

      .back-icon {
        font-size: 18px;
        color: #666;
      }

      .back-text {
        font-size: 10px;
        color: #666;
        margin-top: 2px;
      }
    }

    .contact-btn {
      flex: 1;
      background: #4caf50;
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 0;
      font-size: 16px;
      font-weight: 500;
    }

    .order-btn {
      flex: 1;
      background: #2196f3;
      color: white;
      border: none;
      border-radius: 25px;
      padding: 12px 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;
    gap: 20px;

    text {
      font-size: 16px;
      color: #666;
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;

    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .phone-detail-page {
    .condition-section {
      .condition-item {
        .condition-content {
          flex-direction: column;
          gap: 8px;

          .condition-price {
            min-width: auto;
          }
        }
      }
    }
  }
}
