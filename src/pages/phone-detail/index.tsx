import { useState, useEffect } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import { Button, Toast } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import './index.scss'

// 定义接口数据类型
interface PhoneModel {
  id: number
  parent_id: number
  level: number
  name: string
  sort_index: number
  remake: string
  wechat_tag?: {
    dianliang?: string
    gaobaotianshu?: string
    aisishalouquanlv?: boolean
    wuliangdianliangban?: boolean
    wulaohuatoutu?: boolean
  }
}

interface Brand {
  id: number
  parent_id: number
  level: number
  name: string
  sort_index: number
  remake: string
}

interface SubModel {
  id: number
  sub_model_id: number
  price: number
  tag_name: string
}

interface Tag {
  sub_model_id: number
  id: number
  price_rate: number
  tag_name: string
  group_name: string
}

interface PhoneDetailData {
  model: PhoneModel
  brand: Brand
  classify: Brand
  sub_model: SubModel[]
  tags: Tag[]
}

interface ApiResponse {
  code: number
  data: PhoneDetailData
}

function PhoneDetail() {
  const [phoneData, setPhoneData] = useState<PhoneDetailData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedMemory, setSelectedMemory] = useState<SubModel | null>(null)
  const [selectedCondition, setSelectedCondition] = useState<Tag | null>(null)
  const [currentPrice, setCurrentPrice] = useState<number>(0)
  const [phoneId, setPhoneId] = useState<string>('')

  // 获取URL参数中的手机ID
  useEffect(() => {
    const instance = Taro.getCurrentInstance()
    const id = instance.router?.params?.id
    if (id) {
      setPhoneId(id)
      fetchPhoneDetail(id)
    } else {
      Taro.showToast({
        title: '缺少手机ID参数',
        icon: 'none'
      })
      setTimeout(() => {
        Taro.navigateBack()
      }, 1500)
    }
  }, [])

  // 获取手机详情数据
  const fetchPhoneDetail = async (id: string) => {
    try {
      setLoading(true)
      // 这里使用您提供的API接口格式
      const response = await Taro.request({
        url: `https://www.yizhanhuishou.com/api_price/model/${id}`,
        method: 'GET',
        header: {
          'Content-Type': 'application/json'
        }
      })

      const result = response.data as ApiResponse
      if (result.code === 0 && result.data) {
        setPhoneData(result.data)
        // 默认选择第一个内存规格
        if (result.data.sub_model && result.data.sub_model.length > 0) {
          setSelectedMemory(result.data.sub_model[0])
        }
      } else {
        throw new Error('获取数据失败')
      }
    } catch (error) {
      console.error('获取手机详情失败:', error)
      // 如果API请求失败，使用模拟数据进行演示
      setPhoneData(getMockData(id))
      if (getMockData(id).sub_model && getMockData(id).sub_model.length > 0) {
        setSelectedMemory(getMockData(id).sub_model[0])
      }
    } finally {
      setLoading(false)
    }
  }

  // 计算当前价格
  useEffect(() => {
    if (selectedMemory && selectedCondition) {
      const basePrice = selectedMemory.price
      const rate = selectedCondition.price_rate / 100
      const finalPrice = Math.round(basePrice * rate)
      setCurrentPrice(finalPrice)
    }
  }, [selectedMemory, selectedCondition])

  // 选择内存规格
  const handleMemorySelect = (memory: SubModel) => {
    setSelectedMemory(memory)
    // 重置成色选择
    setSelectedCondition(null)
    setCurrentPrice(0)
  }

  // 选择成色
  const handleConditionSelect = (condition: Tag) => {
    setSelectedCondition(condition)
  }

  // 获取当前内存规格对应的成色选项
  const getConditionsForMemory = () => {
    if (!phoneData || !selectedMemory) return []
    return phoneData.tags.filter(tag => tag.sub_model_id === selectedMemory.sub_model_id)
  }

  // 联系客服
  const handleContactService = () => {
    Taro.showModal({
      title: '联系客服',
      content: '确定要联系客服咨询详情吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到客服页面或拨打电话
          Taro.showToast({
            title: '正在为您转接客服...',
            icon: 'none'
          })
        }
      }
    })
  }

  // 去下单
  const handlePlaceOrder = () => {
    if (!selectedMemory || !selectedCondition) {
      Taro.showToast({
        title: '请选择内存和成色',
        icon: 'none'
      })
      return
    }

    Taro.showModal({
      title: '确认下单',
      content: `${phoneData?.model.name} ${selectedMemory.tag_name} ${selectedCondition.tag_name} ¥${currentPrice}`,
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到下单页面
          Taro.showToast({
            title: '正在跳转下单页面...',
            icon: 'none'
          })
        }
      }
    })
  }

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack()
  }

  if (loading) {
    return (
      <View className='phone-detail-page'>
        <View className='loading-container'>
          <Text className='loading-text'>加载中...</Text>
        </View>
      </View>
    )
  }

  if (!phoneData) {
    return (
      <View className='phone-detail-page'>
        <View className='error-message'>
          <Text>获取手机信息失败</Text>
          <Button size="small" onClick={handleBack}>返回</Button>
        </View>
      </View>
    )
  }

  return (
    <View className='phone-detail-page'>
      {/* 头部导航 */}
      <View className='header'>
        <View className='back-btn' onClick={handleBack}>
          ←
        </View>
        <Text className='header-title'>评估申请</Text>
        <View className='header-actions'>
          <Text className='more-btn'>⋯</Text>
          <Text className='minimize-btn'>−</Text>
          <Text className='close-btn'>○</Text>
        </View>
      </View>

      <ScrollView className='content' scrollY>
        {/* 手机名称 */}
        <View className='phone-title'>
          <Text className='phone-name'>{phoneData.model.name}</Text>
        </View>

        {/* 内存选择 */}
        <View className='selection-section'>
          <View className='section-header'>
            <Text className='section-icon'>📱</Text>
            <Text className='section-title'>内存</Text>
          </View>
          <View className='options-grid'>
            {phoneData.sub_model.map((memory) => (
              <View
                key={memory.sub_model_id}
                className={`option-item ${selectedMemory?.sub_model_id === memory.sub_model_id ? 'selected' : ''}`}
                onClick={() => handleMemorySelect(memory)}
              >
                <Text className='option-text'>{memory.tag_name}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* 购买渠道 */}
        <View className='selection-section'>
          <View className='section-header'>
            <Text className='section-icon'>🛒</Text>
            <Text className='section-title'>购买渠道</Text>
          </View>
          <View className='options-grid'>
            <View className='option-item selected'>
              <Text className='option-text'>大陆国行</Text>
            </View>
          </View>
        </View>

        {/* 注意事项 */}
        {phoneData.model.wechat_tag?.dianliang && (
          <View className='notice-section'>
            <Text className='notice-text'>
              注意：{phoneData.model.wechat_tag.dianliang}
            </Text>
          </View>
        )}

        {/* 实时回收报价 */}
        <View className='price-section'>
          <Text className='price-title'>实时回收报价</Text>

          {phoneData.model.wechat_tag?.gaobaotianshu && (
            <View className='price-notice'>
              <Text className='notice-icon'>🔊</Text>
              <Text className='notice-content'>{phoneData.model.wechat_tag.gaobaotianshu}</Text>
            </View>
          )}
        </View>

        {/* 成色选择和价格 */}
        {selectedMemory && (
          <View className='condition-section'>
            {getConditionsForMemory().map((condition) => (
              <View key={condition.id} className='condition-item'>
                <View className='condition-header'>
                  <Text className='condition-name'>{condition.tag_name}</Text>
                  <View className='condition-tags'>
                    <Text className='tag'>爱思沙漏全绿</Text>
                    <Text className='tag'>无亮点黑斑</Text>
                    <Text className='tag'>无老化透图</Text>
                  </View>
                </View>
                <View className='condition-content'>
                  <Text className='condition-price'>
                    ¥ {Math.round(selectedMemory.price * condition.price_rate / 100)}
                  </Text>
                  <Text className='condition-desc'>
                    {getConditionDescription(condition.tag_name)}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 */}
      <View className='bottom-actions'>
        <View className='back-to-top' onClick={handleBack}>
          <Text className='back-icon'>↶</Text>
          <Text className='back-text'>上一页</Text>
        </View>
        <Button
          className='contact-btn'
          onClick={handleContactService}
        >
          联系客服
        </Button>
        <Button
          className='order-btn'
          type="primary"
          onClick={handlePlaceOrder}
        >
          去下单
        </Button>
      </View>
    </View>
  )
}

// 获取模拟数据
function getMockData(id: string): PhoneDetailData {
  return {
    model: {
      id: parseInt(id),
      parent_id: 1230,
      level: 3,
      name: "苹果15ProMax 在保",
      sort_index: 102,
      remake: "",
      wechat_tag: {
        dianliang: "电池电量≥95%，保修天数＞60天",
        gaobaotianshu: "高保充新价格请来电咨询，全套包装带原装配件加30-300元，蓝色-150",
        aisishalouquanlv: true,
        wuliangdianliangban: true,
        wulaohuatoutu: true
      }
    },
    brand: {
      id: 1230,
      parent_id: 48,
      level: 2,
      name: "iPhone 15系列",
      sort_index: 96,
      remake: ""
    },
    classify: {
      id: 1230,
      parent_id: 48,
      level: 2,
      name: "iPhone 15系列",
      sort_index: 96,
      remake: ""
    },
    sub_model: [
      { id: parseInt(id), sub_model_id: 5459, price: 5950, tag_name: "256G" },
      { id: parseInt(id), sub_model_id: 5461, price: 6500, tag_name: "512G" }
    ],
    tags: [
      { sub_model_id: 5459, id: 386031, price_rate: 100, tag_name: "靓 机", group_name: "成色" },
      { sub_model_id: 5459, id: 386033, price_rate: 90, tag_name: "小 花", group_name: "成色" },
      { sub_model_id: 5459, id: 386035, price_rate: 80, tag_name: "大 花", group_name: "成色" },
      { sub_model_id: 5459, id: 386048, price_rate: 70, tag_name: "外 爆", group_name: "成色" },
      { sub_model_id: 5461, id: 386039, price_rate: 100, tag_name: "靓 机", group_name: "成色" },
      { sub_model_id: 5461, id: 386042, price_rate: 90, tag_name: "小 花", group_name: "成色" },
      { sub_model_id: 5461, id: 386060, price_rate: 80, tag_name: "大 花", group_name: "成色" },
      { sub_model_id: 5461, id: 386052, price_rate: 70, tag_name: "外 爆", group_name: "成色" }
    ]
  }
}

// 获取成色描述
function getConditionDescription(conditionName: string): string {
  const descriptions: Record<string, string> = {
    '靓 机': '外观轻微使用痕迹或细微瑕疵，屏幕',
    '小 花': '屏幕细微划痕，外观划痕/小磕≤3毫米',
    '大 花': '屏幕轻微划痕≤10毫米，外壳支架不缺失或断裂',
    '外 爆': '屏幕碎裂，严重硬化痕，小碎角，显示无压斑亮斑，外壳支架不缺失断裂，机身不弯曲变形'
  }
  return descriptions[conditionName] || ''
}

export default PhoneDetail
