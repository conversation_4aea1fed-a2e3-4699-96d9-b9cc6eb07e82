.home-page {
  min-height: 100vh;
  background: linear-gradient(
    180deg,
    rgba(205, 224, 250, 1) 0%,
    rgba(255, 255, 255, 1) 100%
  );

  .search-section {
  }

  .banner-section {
    margin: 10px 20px;
    border-radius: 12px;
    overflow: hidden;

    .banner-item {
      position: relative;
      height: 160px;

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
        padding: 20px;

        .banner-title {
          color: white;
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
        }

        .banner-subtitle {
          color: white;
          font-size: 14px;
          font-weight: 400;
          opacity: 0.9;
        }
      }
    }
  }

  .notice-section {
    margin: 10px 20px;
    border-radius: 8px;
    overflow: hidden;
  }

  .price-card-section {
    margin: 10px 20px;
    .price-card {
      position: relative;
      background-image: url("https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%88%86%E7%BB%84%204.png");
      background-size: 100%;
      border-radius: 12px;
      height: 40px;
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

      .price-card-left {
        display: flex;
        align-items: center;

        .phone-icon {
          font-size: 32px;
          margin-right: 12px;
        }

        .price-info {
          .price-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
          }

          .price-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
          }
        }
      }

      .price-card-right {
        margin-left: 218px;
        .check-price-btn {
          width: 68px;
          height: 28px;
          opacity: 1;
          border-radius: 5px;
          background: rgba(237, 249, 255, 1);
          padding: 4px 13px;
        }
      }
    }
  }

  .contact-actions-section {
    margin: 10px 20px;
    border-radius: 12px;
    color: rgba(28, 148, 211, 1);
    .contact-section-title {
      margin-bottom: 20px;

      .title-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }

      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .contact-actions {
      display: flex;
      justify-content: space-around;
      align-items: center;

      .contact-action-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 20px;
        border: 1px solid rgba(28, 148, 211, 1);
        margin-right: 12px;
        &:active {
          transform: scale(0.95);
        }

        .phone-icon {
          width: 20px;
          height: 20px;
          margin: 0 5px;
        }

        .action-icon-circle {
          width: 50px;
          height: 50px;
          border-radius: 25px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          margin-bottom: 8px;
          border: 2px solid #e8e8e8;
          background: white;

          &.phone-circle {
            color: #52c41a;
            border-color: #52c41a;
          }

          &.wechat-circle {
            color: #1890ff;
            border-color: #1890ff;
          }

          &.share-circle {
            color: #fa8c16;
            border-color: #fa8c16;
          }
        }

        .action-text {
          font-size: 12px;
          color: #666;
          font-weight: 500;
          text-align: center;
        }
      }
      .contact-action-item:last-child{
        margin-right:0;
      }
    }
  }

  .store-section {
    margin: 10px 20px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .store-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .store-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-right: 8px;
      }

      .store-badge {
        background: #ff4d4f;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
      }
    }

    .store-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .store-content {
        display: flex;

        .store-image {
          width: 115px;
          margin-right: 16px;
          border-radius: 8px;
          overflow: hidden;
          flex-shrink: 0;

          .store-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .store-info {
          flex: 1;
          .contact-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .contact-label {
              font-size: 14px;
              color: #333;
              margin-right: 8px;
              min-width: 100px;
            }

            .contact-number {
              font-size: 16px;
              color: #1890ff;
              font-weight: 500;
              margin-right: 12px;
              text-decoration: underline;
            }

            .contact-icons {
              display: flex;
              gap: 8px;

              .contact-icon {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;

                &:active {
                  transform: scale(0.95);
                }

                .icon-image {
                  width: 20px;
                  height: 20px;
                }

                &.phone-green {
                  background: #52c41a;
                }

                &.wechat-blue {
                  background: #1890ff;
                }
              }
            }
          }

          .address-row {
            display: flex;
            align-items: flex-start;
            margin-top: 8px;

            .address-text {
              font-size: 14px;
              color: #333;
              flex: 1;
              margin-right: 12px;
              line-height: 1.4;
            }

            .location-icon {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #1890ff;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.3s ease;

              &:active {
                transform: scale(0.95);
              }

              .icon-image {
                width: 20px;
                height: 20px;
              }
            }
          }
        }
      }
    }
  }

  .price-list-section {
    margin: 10px 20px;
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .price-list-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }

    .price-list {
      .price-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .phone-name {
          font-size: 14px;
          color: #333;
          flex: 1;
          margin-right: 12px;
          line-height: 1.4;
        }

        .price-change {
          .price-trend {
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;

            &.up {
              color: #ff4d4f;
            }

            &.down {
              color: #52c41a;
            }
          }
        }
      }
    }
  }
}
