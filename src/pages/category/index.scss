// 全局覆盖NutUI Cell的圆角和间距
.nut-cell,
.nut-cell-group,
.nut-cell-group__wrap,
.h5-div.nut-cell-group__wrap {
  border-radius: 0 !important;
  margin: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.category-page {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .search-section {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 20px 16px 10px;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .main-content {
    flex: 1;
    display: flex;
    background-color: #fff;
    height: calc(100vh - 80px); // 减去搜索栏的高度
    margin-top: 80px; // 为固定搜索栏预留空间

    .left-sidebar {
      width: 120px;
      background-color: #f3f3f3;
      border-right: 1px solid #f0f0f0;
      overflow-y: auto;
      flex-shrink: 0;

      // 左侧分类CellGroup样式
      .category-cell-group {
        background-color: transparent;
        border-radius: 0 !important;
        margin: 0 !important;

        .nut-cell-group__wrap {
          margin: 0 !important;
        }

        // 更强的选择器覆盖NutUI默认样式
        .nut-cell {
          margin: 2px 0 !important;
          padding: 12px 12px !important;
          border-radius: 0 !important;
        }
      }

      // 左侧分类Cell样式
      .category-cell {
        background-color: transparent;
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 12px !important;
        margin: 2px 0 !important; // 添加Cell之间的上下间距
        font-size: 14px;
        position: relative;
        border-radius: 0 !important;

        // 确保NutUI内部元素也没有圆角
        * {
          border-radius: 0 !important;
        }

        &.active {
          background-color: #fff;
          color: #1890ff !important;

          // 确保NutUI Cell内部的文字也变色
          .nut-cell__title {
            color: #1890ff !important;
          }

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 2px;
            height: 1.2em; // 跟文字高度一致
            background-color: #1890ff;
            border-radius: 0 3px 3px 0; // 右侧圆角
          }
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .sub-category-nav {
        background-color: #fff;
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 0;
        flex-shrink: 0;

        .nav-wrapper {
          display: flex;
          white-space: nowrap;
          gap: 8px;
          padding: 0 16px;
          overflow-x: auto;
          scrollbar-width: none; // Firefox
          -ms-overflow-style: none; // IE

          &::-webkit-scrollbar {
            display: none; // Chrome, Safari
          }

          .sub-category-title {
            display: inline-block;
            padding: 8px 16px;
            font-size: 12px;
            color: #666;
            background-color: #f5f5f5;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            flex-shrink: 0;

            &.active {
              color: #fff;
              background-color: #1890ff;
            }

            &:hover {
              background-color: #e6f7ff;
              color: #1890ff;
            }

            &.active:hover {
              background-color: #1890ff;
              color: #fff;
            }
          }
        }
      }

      .scroll-content {
        flex: 1;
        padding: 16px;
        overflow-y: auto;

        .sub-category-section {
          margin-bottom: 16px;

          .sub-category-section-title {
            margin-bottom: 8px;

            .section-title-text {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              position: relative;
              padding-left: 12px;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 3px;
                height: 3px;
                background-color: #1890ff;
                border-radius: 50%;
              }
            }
          }

          // 商品CellGroup样式
          .product-cell-group {
            border-radius: 0 !important;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 0 !important; // 强制去除所有margin
            padding: 0; // 去除CellGroup内部的padding，避免第一个和最后一个项目有额外间距

            .nut-cell-group__wrap {
              margin: 0 !important;
            }

            // 更强的选择器覆盖NutUI默认样式
            .nut-cell {
              margin: 2px 0 !important;
              padding: 6px 16px !important;
              border-radius: 0 !important;
            }
          }

          // 商品Cell样式
          .product-cell {
            padding: 6px 16px !important; // 上下6px，左右16px
            margin: 2px 0 !important; // 添加Cell之间的上下间距
            font-size: 14px;
            border-bottom: 1px solid #f0f0f0;
            border-radius: 0 !important;

            // 确保NutUI内部元素也没有圆角
            * {
              border-radius: 0 !important;
            }

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
}
