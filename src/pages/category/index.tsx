import { useState, useEffect } from 'react'
import { View, Text } from '@tarojs/components'
import { SearchBar, Cell, CellGroup } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import './index.scss'
import { api } from './../../services/index'

interface CategoryItem {
  id: number
  name: string
  parent_id: number
  level: number
  children?: CategoryItem[]
}

function Category() {
  const [searchValue, setSearchValue] = useState('')
  const [categories, setCategories] = useState<CategoryItem[]>([])
  const [selectedCategory, setSelectedCategory] = useState<CategoryItem | null>(null)
  const [selectedSubCategory, setSelectedSubCategory] = useState<CategoryItem | null>(null)

  const [loading, setLoading] = useState(true)




  const getCategory = async ()=>{
    api.category.getTree().then((res)=>{
      console.log(res)
      setCategories(res)
      setSelectedCategory(res[0])
      // 设置默认选中第一个子分类
      if (res[0] && res[0].children && res[0].children.length > 0) {
        setSelectedSubCategory(res[0].children[0])
      }
      setLoading(false)
    })
  }
   // 初始化数据
  useEffect(() => {
    getCategory()
  }, [])

  const handleSearch = (value: string) => {
    console.log('搜索:', value)
    // 跳转到搜索页面
    Taro.navigateTo({
      url: `/pages/search/index?keyword=${encodeURIComponent(value)}`
    })
  }

  // 处理搜索栏点击
  const handleSearchClick = () => {
    Taro.navigateTo({
      url: '/pages/search/index'
    })
  }

  const handleCategoryClick = (category: CategoryItem) => {
    setSelectedCategory(category)
    // 设置默认选中第一个子分类
    if (category.children && category.children.length > 0) {
      setSelectedSubCategory(category.children[0])
    } else {
      setSelectedSubCategory(null)
    }
  }

  // 处理子分类标题点击
  const handleSubCategoryClick = (subCategory: CategoryItem) => {
    console.log('点击子分类:', subCategory.name, `sub-category-${subCategory.id}`)
    setSelectedSubCategory(subCategory)
  }

  const handleItemClick = (item: CategoryItem) => {
    console.log('点击商品:', item)
    // 跳转到手机详情页
    Taro.navigateTo({
      url: `/pages/phone-detail/index?id=${item.id}`
    })
  }

  if (loading) {
    return (
      <View className='category-page'>
        <View className='loading'>加载中...</View>
      </View>
    )
  }

  return (
    <View className='category-page'>
      {/* 搜索栏 */}
      <View className='search-section' onClick={handleSearchClick}>
        <SearchBar
          value={searchValue}
          placeholder='苹果16promax'
          onSearch={handleSearch}
          onChange={setSearchValue}
          readOnly
        />
      </View>

      {/* 主要内容区域 */}
      <View className='main-content'>
        {/* 左侧分类列表 */}
        <View className='left-sidebar'>
          <CellGroup className='category-cell-group'>
            {categories.map(category => (
              <Cell
                key={category.id}
                title={category.name}
                className={`category-cell ${selectedCategory?.id === category.id ? 'active' : ''}`}
                onClick={() => handleCategoryClick(category)}
              />
            ))}
          </CellGroup>
        </View>

        {/* 右侧内容区域 */}
        <View className='right-content'>
          {/* 子分类标题导航 */}
          {selectedCategory && selectedCategory.children && selectedCategory.children.length > 0 && (
            <View className='sub-category-nav'>
              <View className='nav-wrapper'>
                {selectedCategory.children.map(subCategory => (
                  <View
                    key={subCategory.id}
                    className={`sub-category-title ${selectedSubCategory?.id === subCategory.id ? 'active' : ''}`}
                    onClick={() => handleSubCategoryClick(subCategory)}
                  >
                    <Text>{subCategory.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* 滚动内容区域 */}
          <View className='scroll-content'>
            {selectedCategory && selectedCategory.children && (
              <>
                {selectedCategory.children.map(subCategory => (
                  <View
                    key={subCategory.id}
                    id={`sub-category-${subCategory.id}`}
                    className='sub-category-section'
                  >
                    {/* 子分类标题 */}
                    <View className='sub-category-section-title'>
                      <Text className='section-title-text'>{subCategory.name}</Text>
                    </View>
                    {/* 商品列表 */}
                    <CellGroup className='product-cell-group'>
                      {subCategory.children && subCategory.children.map(product => (
                        <Cell
                          key={product.id}
                          title={product.name}
                          className='product-cell'
                          onClick={() => handleItemClick(product)}
                        />
                      ))}
                    </CellGroup>
                  </View>
                ))}
              </>
            )}
          </View>
        </View>
      </View>
    </View>
  )
}

export default Category
