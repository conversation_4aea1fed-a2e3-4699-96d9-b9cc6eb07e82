import { View, Image } from '@tarojs/components'
import './index.scss'

function Profile() {
  const defauletSrc = "https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/WechatIMG69.jpg?Expires=1750579298&OSSAccessKeyId=TMP.3KoGk4yUHoK9byqTAxizFbJkvG2qAoSDTfBdqkojxDM1Q2yeNKzcx7f7uEfJhs3n2HeCidMUBw8eY5CrX526P4cCvgruKe&Signature=eT5NRqVsV4N3TV2rj%2F47Nt3nM7U%3D"
  return (
    <View>
         <Image
        src={defauletSrc}
        width='100'
        height={100}
        onClick={() => {
          console.log('click image')
        }}
      />
    </View>
  )
}

export default Profile
