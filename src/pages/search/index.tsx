import { useState, useEffect } from 'react'
import { View, Text, ScrollView } from '@tarojs/components'
import { SearchBar, Empty } from '@nutui/nutui-react-taro'
import Taro from '@tarojs/taro'
import './index.scss'

interface SearchResult {
  id: number
  name: string
  category: string
  price?: string
}

function Search() {
  const [searchValue, setSearchValue] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)

  // 从本地存储加载搜索历史
  useEffect(() => {
    const history = Taro.getStorageSync('searchHistory') || []
    setSearchHistory(history)

    // 获取URL参数中的关键词
    const instance = Taro.getCurrentInstance()
    const keyword = instance.router?.params?.keyword
    if (keyword) {
      const decodedKeyword = decodeURIComponent(keyword)
      setSearchValue(decodedKeyword)
      // 延迟执行搜索，确保组件已完全加载
      setTimeout(() => {
        handleSearch(decodedKeyword)
      }, 100)
    }
  }, [])

  // 模拟搜索API
  const mockSearchAPI = async (keyword: string): Promise<SearchResult[]> => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟搜索结果
    const allResults: SearchResult[] = [
      { id: 1239, name: '苹果16ProMax 在保', category: 'iPhone 16系列', price: '¥8000-9000' },
      { id: 1240, name: '苹果16Pro 在保', category: 'iPhone 16系列', price: '¥7000-8000' },
      { id: 1241, name: '苹果16Plus 在保', category: 'iPhone 16系列', price: '¥6000-7000' },
      { id: 1242, name: '苹果16 在保', category: 'iPhone 16系列', price: '¥5000-6000' },
      { id: 1243, name: '苹果15ProMax 在保', category: 'iPhone 15系列', price: '¥7000-8000' },
      { id: 1244, name: '苹果15Pro 在保', category: 'iPhone 15系列', price: '¥6000-7000' },
      { id: 1245, name: '苹果15Plus 在保', category: 'iPhone 15系列', price: '¥5000-6000' },
      { id: 1246, name: '苹果15 在保', category: 'iPhone 15系列', price: '¥4500-5500' },
      { id: 1247, name: '华为Mate70Pro+ PLA-AL10', category: 'Mate系列', price: '¥6000-7000' },
      { id: 1248, name: '华为Mate70Pro PLR-AL00', category: 'Mate系列', price: '¥5500-6500' }
    ]

    // 根据关键词过滤结果
    return allResults.filter(item =>
      item.name.toLowerCase().includes(keyword.toLowerCase()) ||
      item.category.toLowerCase().includes(keyword.toLowerCase())
    )
  }

  // 处理搜索
  const handleSearch = async (value: string) => {
    if (!value.trim()) {
      setSearchResults([])
      setShowResults(false)
      return
    }

    setLoading(true)
    setShowResults(true)

    try {
      const results = await mockSearchAPI(value.trim())
      setSearchResults(results)

      // 保存到搜索历史
      saveToHistory(value.trim())
    } catch (error) {
      console.error('搜索失败:', error)
      Taro.showToast({
        title: '搜索失败',
        icon: 'none'
      })
    } finally {
      setLoading(false)
    }
  }

  // 保存搜索历史
  const saveToHistory = (keyword: string) => {
    const newHistory = [keyword, ...searchHistory.filter(item => item !== keyword)].slice(0, 10)
    setSearchHistory(newHistory)
    Taro.setStorageSync('searchHistory', newHistory)
  }

  // 处理历史记录点击
  const handleHistoryClick = (keyword: string) => {
    setSearchValue(keyword)
    handleSearch(keyword)
  }

  // 清除搜索历史
  const clearHistory = () => {
    setSearchHistory([])
    Taro.removeStorageSync('searchHistory')
  }

  // 删除单个历史记录
  const removeHistoryItem = (keyword: string) => {
    const newHistory = searchHistory.filter(item => item !== keyword)
    setSearchHistory(newHistory)
    Taro.setStorageSync('searchHistory', newHistory)
  }

  // 处理商品点击
  const handleProductClick = (product: SearchResult) => {
    console.log('点击商品:', product)
    // 跳转到手机详情页
    Taro.navigateTo({
      url: `/pages/phone-detail/index?id=${product.id}`
    })
  }

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack()
  }

  return (
    <View className='search-page'>
      {/* 搜索栏 */}
      <View className='search-header'>
        <View className='back-btn' onClick={handleBack}>
          ←
        </View>
        <View className='search-input-wrapper'>
          <SearchBar
            value={searchValue}
            placeholder='搜索商品'
            onSearch={handleSearch}
            onChange={setSearchValue}
            autoFocus
          />
        </View>
      </View>

      {/* 搜索结果 */}
      {showResults ? (
        <ScrollView className='search-results' scrollY>
          {loading ? (
            <View className='loading'>搜索中...</View>
          ) : searchResults.length > 0 ? (
            <>
              <View className='results-header'>
                <Text className='results-count'>找到 {searchResults.length} 个结果</Text>
              </View>
              {searchResults.map(product => (
                <View
                  key={product.id}
                  className='result-item'
                  onClick={() => handleProductClick(product)}
                >
                  <View className='product-info'>
                    <Text className='product-name'>{product.name}</Text>
                    <Text className='product-category'>{product.category}</Text>
                    {product.price && (
                      <Text className='product-price'>{product.price}</Text>
                    )}
                  </View>
                  <View className='arrow'>→</View>
                </View>
              ))}
            </>
          ) : (
            <Empty description="暂无搜索结果" />
          )}
        </ScrollView>
      ) : (
        /* 搜索历史 */
        <ScrollView className='search-history' scrollY>
          {searchHistory.length > 0 && (
            <>
              <View className='history-header'>
                <Text className='history-title'>搜索历史</Text>
                <Text className='clear-btn' onClick={clearHistory}>清除</Text>
              </View>
              <View className='history-list'>
                {searchHistory.map((keyword, index) => (
                  <View key={index} className='history-item'>
                    <View
                      className='history-keyword'
                      onClick={() => handleHistoryClick(keyword)}
                    >
                      <Text className='keyword-text'>{keyword}</Text>
                    </View>
                    <View
                      className='delete-btn'
                      onClick={() => removeHistoryItem(keyword)}
                    >
                      ×
                    </View>
                  </View>
                ))}
              </View>
            </>
          )}

          {/* 热门搜索 */}
          <View className='hot-search'>
            <Text className='hot-title'>热门搜索</Text>
            <View className='hot-tags'>
              {['iPhone 16', 'iPhone 15', '华为Mate70', 'iPad', 'MacBook'].map(tag => (
                <View
                  key={tag}
                  className='hot-tag'
                  onClick={() => handleHistoryClick(tag)}
                >
                  <Text>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      )}
    </View>
  )
}

export default Search
