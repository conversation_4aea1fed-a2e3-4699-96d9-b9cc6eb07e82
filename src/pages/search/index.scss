.search-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .search-header {
    background: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .back-btn {
      font-size: 20px;
      color: #333;
      padding: 4px;
      cursor: pointer;
    }

    .search-input-wrapper {
      flex: 1;
    }
  }

  .search-results {
    flex: 1;
    background: white;
    margin-top: 8px;

    .loading {
      text-align: center;
      padding: 40px 20px;
      color: #666;
    }

    .results-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      .results-count {
        font-size: 14px;
        color: #666;
      }
    }

    .result-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #f5f5f5;
      }

      .product-info {
        flex: 1;

        .product-name {
          display: block;
          font-size: 16px;
          color: #333;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .product-category {
          display: block;
          font-size: 12px;
          color: #999;
          margin-bottom: 4px;
        }

        .product-price {
          display: block;
          font-size: 14px;
          color: #ff6b6b;
          font-weight: 500;
        }
      }

      .arrow {
        font-size: 16px;
        color: #ccc;
      }
    }
  }

  .search-history {
    flex: 1;
    background: white;
    margin-top: 8px;

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      .history-title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .clear-btn {
        font-size: 14px;
        color: #666;
        cursor: pointer;

        &:active {
          color: #333;
        }
      }
    }

    .history-list {
      .history-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .history-keyword {
          flex: 1;
          cursor: pointer;

          .keyword-text {
            font-size: 14px;
            color: #333;
          }

          &:active {
            opacity: 0.7;
          }
        }

        .delete-btn {
          font-size: 18px;
          color: #ccc;
          padding: 4px;
          cursor: pointer;

          &:active {
            color: #999;
          }
        }
      }
    }

    .hot-search {
      margin-top: 24px;
      padding: 16px;

      .hot-title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
        margin-bottom: 16px;
        display: block;
      }

      .hot-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .hot-tag {
          padding: 8px 16px;
          background: #f5f5f5;
          border-radius: 20px;
          cursor: pointer;

          &:active {
            background: #e0e0e0;
          }

          text {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
}
