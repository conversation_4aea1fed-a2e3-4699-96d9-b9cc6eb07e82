import React from 'react'
import { View, Text, Button } from '@tarojs/components'
// 暂时注释掉有问题的 NutUI 组件
// import { Button, Cell, CellGroup } from '@nutui/nutui-react-taro'
import './index.scss'

function Test() {
  const handleClick = () => {
    console.log('测试按钮点击')
  }

  return (
    <View className='test-page'>
      <View className='test-section'>
        <Text className='test-title'>基础组件测试</Text>

        <View className='test-item'>
          <Text className='item-title'>按钮组件测试</Text>
          <Button type='primary' onClick={handleClick}>
            主要按钮
          </Button>
        </View>

        <View className='test-item'>
          <Text className='item-title'>Cell 组件测试（基础版）</Text>
          <View className='cell-group'>
            <View className='cell-item'>
              <Text className='cell-title'>单元格1</Text>
              <Text className='cell-desc'>描述信息</Text>
            </View>
            <View className='cell-item'>
              <Text className='cell-title'>单元格2</Text>
              <Text className='cell-desc'>描述信息</Text>
            </View>
          </View>
        </View>

        <View className='test-item'>
          <Text className='item-title'>基础组件测试</Text>
          <View className='basic-test'>
            <Text>这是一个基础的 Text 组件</Text>
            <View className='test-box'>这是一个 View 组件</View>
          </View>
        </View>
      </View>
    </View>
  )
}

export default Test
