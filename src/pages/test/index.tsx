import React from 'react'
import { View, Text } from '@tarojs/components'
import { Button, Cell, CellGroup } from '@nutui/nutui-react-taro'
import './index.scss'

function Test() {
  const handleClick = () => {
    console.log('测试按钮点击')
  }

  return (
    <View className='test-page'>
      <View className='test-section'>
        <Text className='test-title'>NutUI 组件测试</Text>
        
        <View className='test-item'>
          <Text className='item-title'>按钮组件测试</Text>
          <Button type='primary' onClick={handleClick}>
            主要按钮
          </Button>
        </View>

        <View className='test-item'>
          <Text className='item-title'>Cell 组件测试</Text>
          <CellGroup>
            <Cell title='单元格1' desc='描述信息' />
            <Cell title='单元格2' desc='描述信息' />
          </CellGroup>
        </View>

        <View className='test-item'>
          <Text className='item-title'>基础组件测试</Text>
          <View className='basic-test'>
            <Text>这是一个基础的 Text 组件</Text>
            <View className='test-box'>这是一个 View 组件</View>
          </View>
        </View>
      </View>
    </View>
  )
}

export default Test
