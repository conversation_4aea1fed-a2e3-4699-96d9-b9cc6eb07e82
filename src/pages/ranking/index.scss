.ranking-page {
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: linear-gradient(135deg, #1890ff, #36cfc9);
    padding: 30px 20px 20px;
    color: white;
    text-align: center;

    .header-title {
      font-size: 24px;
      font-weight: 600;
      display: block;
      margin-bottom: 8px;
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
    }
  }

  .ranking-list {
    padding: 10px 20px;

    .ranking-item {
      background: white;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s;

      &:active {
        transform: scale(0.98);
      }

      .rank-badge {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        background: #f0f0f0;

        .rank-number {
          font-size: 16px;
          font-weight: 600;
        }
      }

      &.rising .rank-badge {
        background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
        color: white;
      }

      &.falling .rank-badge {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        color: white;
      }

      &.hot .rank-badge {
        background: linear-gradient(135deg, #faad14, #ffc53d);
        color: white;
      }

      .item-info {
        flex: 1;
        margin-right: 16px;

        .item-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }

        .item-category {
          font-size: 12px;
          color: #999;
          background: #f0f0f0;
          padding: 2px 8px;
          border-radius: 4px;
          display: inline-block;
        }
      }

      .price-info {
        text-align: right;

        .current-price {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }

        .change-info {
          &.rising {
            .change-percent {
              color: #ff4d4f;
            }
            .change-value {
              color: #ff4d4f;
            }
          }

          &.falling {
            .change-percent {
              color: #52c41a;
            }
            .change-value {
              color: #52c41a;
            }
          }

          .change-percent {
            font-size: 14px;
            font-weight: 600;
            display: block;
          }

          .change-value {
            font-size: 12px;
          }
        }
      }

      .hot-info {
        text-align: right;

        .current-price {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 8px;
        }

        .heat-info {
          .volume-text {
            font-size: 12px;
            color: #999;
            display: block;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}
