import React, { useState } from 'react'
import { View, Text } from '@tarojs/components'
// 暂时注释掉有问题的 NutUI 组件
// import { Tabs, TabPane, Tag, Progress } from '@nutui/nutui-react-taro'
import './index.scss'

function Ranking() {
  const [activeTab, setActiveTab] = useState('0')

  // 模拟涨跌榜数据
  const rankingData = {
    rising: [
      { rank: 1, name: 'iPhone 15 Pro Max', category: '手机数码', price: '¥6800', change: '+15.2%', changeValue: '+800' },
      { rank: 2, name: 'MacBook Pro M3', category: '电脑办公', price: '¥12500', change: '+12.8%', changeValue: '+1420' },
      { rank: 3, name: 'iPad Pro 12.9', category: '平板电脑', price: '¥4200', change: '+10.5%', changeValue: '+400' },
      { rank: 4, name: 'AirPods Pro 2', category: '音频设备', price: '¥1450', change: '****%', changeValue: '+118' },
      { rank: 5, name: 'Apple Watch Ultra', category: '智能穿戴', price: '¥3800', change: '****%', changeValue: '+268' }
    ],
    falling: [
      { rank: 1, name: 'iPhone 13', category: '手机数码', price: '¥3200', change: '-8.5%', changeValue: '-298' },
      { rank: 2, name: 'iPad Air 4', category: '平板电脑', price: '¥2800', change: '-6.2%', changeValue: '-185' },
      { rank: 3, name: 'MacBook Air M1', category: '电脑办公', price: '¥6800', change: '-5.8%', changeValue: '-418' },
      { rank: 4, name: 'AirPods 3', category: '音频设备', price: '¥980', change: '-4.9%', changeValue: '-50' },
      { rank: 5, name: 'Apple Watch SE', category: '智能穿戴', price: '¥1680', change: '-3.7%', changeValue: '-64' }
    ],
    hot: [
      { rank: 1, name: 'iPhone 15', category: '手机数码', price: '¥4500', volume: 1250, heat: 95 },
      { rank: 2, name: 'MacBook Pro', category: '电脑办公', price: '¥8800', volume: 890, heat: 88 },
      { rank: 3, name: 'iPad', category: '平板电脑', price: '¥2200', volume: 756, heat: 82 },
      { rank: 4, name: 'AirPods Pro', category: '音频设备', price: '¥1200', volume: 645, heat: 76 },
      { rank: 5, name: 'Apple Watch', category: '智能穿戴', price: '¥2100', volume: 523, heat: 71 }
    ]
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇'
      case 2: return '🥈'
      case 3: return '🥉'
      default: return rank.toString()
    }
  }

  const handleItemClick = (item: any) => {
    console.log('点击商品:', item)
  }

  return (
    <View className='ranking-page'>
      <View className='page-header'>
        <Text className='header-title'>回收价格榜</Text>
        <Text className='header-subtitle'>实时更新，把握市场动态</Text>
      </View>

      {/* 简化的 Tab 切换 */}
      <View className='tabs-container'>
        <View className='tab-headers'>
          <View
            className={`tab-header ${activeTab === '0' ? 'active' : ''}`}
            onClick={() => setActiveTab('0')}
          >
            <Text>涨价榜</Text>
          </View>
          <View
            className={`tab-header ${activeTab === '1' ? 'active' : ''}`}
            onClick={() => setActiveTab('1')}
          >
            <Text>降价榜</Text>
          </View>
          <View
            className={`tab-header ${activeTab === '2' ? 'active' : ''}`}
            onClick={() => setActiveTab('2')}
          >
            <Text>热门榜</Text>
          </View>
        </View>

        {/* 涨价榜 */}
        {activeTab === '0' && (
          <View className='ranking-list'>
            {rankingData.rising.map((item, index) => (
              <View key={index} className='ranking-item rising' onClick={() => handleItemClick(item)}>
                <View className='rank-badge'>
                  <Text className='rank-number'>{getRankIcon(item.rank)}</Text>
                </View>
                <View className='item-info'>
                  <Text className='item-name'>{item.name}</Text>
                  <Text className='item-category'>{item.category}</Text>
                </View>
                <View className='price-info'>
                  <Text className='current-price'>{item.price}</Text>
                  <View className='change-info rising'>
                    <Text className='change-percent'>{item.change}</Text>
                    <Text className='change-value'>{item.changeValue}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* 降价榜 */}
        {activeTab === '1' && (
          <View className='ranking-list'>
            {rankingData.falling.map((item, index) => (
              <View key={index} className='ranking-item falling' onClick={() => handleItemClick(item)}>
                <View className='rank-badge'>
                  <Text className='rank-number'>{getRankIcon(item.rank)}</Text>
                </View>
                <View className='item-info'>
                  <Text className='item-name'>{item.name}</Text>
                  <Text className='item-category'>{item.category}</Text>
                </View>
                <View className='price-info'>
                  <Text className='current-price'>{item.price}</Text>
                  <View className='change-info falling'>
                    <Text className='change-percent'>{item.change}</Text>
                    <Text className='change-value'>{item.changeValue}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* 热门榜 */}
        {activeTab === '2' && (
          <View className='ranking-list'>
            {rankingData.hot.map((item, index) => (
              <View key={index} className='ranking-item hot' onClick={() => handleItemClick(item)}>
                <View className='rank-badge'>
                  <Text className='rank-number'>{getRankIcon(item.rank)}</Text>
                </View>
                <View className='item-info'>
                  <Text className='item-name'>{item.name}</Text>
                  <Text className='item-category'>{item.category}</Text>
                </View>
                <View className='hot-info'>
                  <Text className='current-price'>{item.price}</Text>
                  <View className='heat-info'>
                    <Text className='volume-text'>成交 {item.volume}</Text>
                    <View className='progress-bar'>
                      <View
                        className='progress-fill'
                        style={{ width: `${item.heat}%` }}
                      ></View>
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    </View>
  )
}

export default Ranking
