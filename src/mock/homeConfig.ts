// 首页配置模拟数据
export const mockHomeConfig = {
  notices: [
    {
      id: 1,
      content: '📢 如有闲置手机请告知回收人员',
      type: 'info'
    },
    {
      id: 2,
      content: '🎉 新用户注册即享专属优惠，最高可获得额外100元回收金',
      type: 'promotion'
    },
    {
      id: 3,
      content: '💰 高价回收，秒到账，安全可靠，已为10万+用户提供服务',
      type: 'info'
    },
    {
      id: 4,
      content: '🔥 iPhone 15系列回收价格上调，现在出售正当时',
      type: 'hot'
    },
    {
      id: 5,
      content: '⚡ 当面交易，现场验机，价格透明，无隐藏费用',
      type: 'info'
    }
  ],
  contact: {
    phone: '18210924745',
    wechat: 'baofeng_recycle_2024',
    shareContent: {
      title: '暴风回收 - 专业手机回收平台，高价回收，秒到账',
      path: '/pages/index/index',
      imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
    }
  },
  banners: [
    {
      id: 1,
      image: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png',
      title: '暴风数码',
      subtitle: '高价/秒/到/账',
      link: ''
    }
  ],
  stores: [
    {
      id: 1,
      name: '暴风回收成都店',
      image: 'https://wx-baofeng-bucket.oss-cn-beijing.aliyuncs.com/%E5%9B%BE%E6%A0%87/685552956ef735a47335a83c.jpg',
      salesPhone: '18628303777',
      salesWechat: '18628303777',
      recyclePhone: '18628303777',
      recycleWechat: '18628303777',
      address: '四川省成都市青羊区赛格广场一楼5032-5033号',
      isHot: true
    },
  ]
};

// 模拟API延迟
export const mockApiDelay = (data: any, delay: number = 1000): Promise<any> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(data);
    }, delay);
  });
};
