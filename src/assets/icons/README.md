# 图标资源说明

这个目录用于存放底部导航栏的图标文件。

## 已配置的图标文件路径

根据 `src/app.config.ts` 中的 tabBar 配置，需要准备以下图标文件：

### 普通状态图标（未选中）
- `home.png` - 首页图标
- `category.png` - 类目图标
- `ranking.png` - 涨跌榜图标
- `profile.png` - 我的图标

### 选中状态图标
- `home-active.png` - 首页选中图标
- `category-active.png` - 类目选中图标
- `ranking-active.png` - 涨跌榜选中图标
- `profile-active.png` - 我的选中图标

## 图标规格要求

1. **文件格式**: PNG（推荐）或 JPG
2. **图标尺寸**: 78x78px（微信小程序推荐尺寸）
3. **文件大小**: 每个图标文件不超过 40KB
4. **设计规范**:
   - 普通状态图标：使用灰色调（#666666）
   - 选中状态图标：使用品牌色（#1890ff）
   - 图标风格保持一致
   - 线条粗细适中，确保在小尺寸下清晰可见

## 图标设计建议

### 首页图标 (home)
- 普通状态：灰色房子图标
- 选中状态：蓝色房子图标

### 类目图标 (category)
- 普通状态：灰色网格或分类图标
- 选中状态：蓝色网格或分类图标

### 涨跌榜图标 (ranking)
- 普通状态：灰色趋势图或排行榜图标
- 选中状态：蓝色趋势图或排行榜图标

### 我的图标 (profile)
- 普通状态：灰色用户头像图标
- 选中状态：蓝色用户头像图标

## 使用说明

1. 将准备好的图标文件放置在 `src/assets/icons/` 目录下
2. 确保文件名与配置中的路径完全匹配
3. 重新编译项目即可看到底部导航栏的图标效果

## 临时解决方案

如果暂时没有图标文件，可以：
1. 使用在线图标生成工具创建简单图标
2. 从免费图标库下载合适的图标（如 iconfont、iconify 等）
3. 使用设计工具（如 Figma、Sketch）自行设计
