// NutUI 变量定义文件
// 用于解决 NutUI 组件样式编译错误

// 颜色变量
$primary-color: #fa2c19;
$primary-color-end: #fa6419;
$help-color: #909ca4;
$title-color: #1a1a1a;
$title-color2: #666666;
$text-color: #808080;
$disable-color: #cccccc;
$border-color: #f1f1f1;
$primary-text-color: #fa2c19;

// 灰度色阶
$white: #ffffff;
$gray-1: #f7f8fa;
$gray-2: #f2f3f5;
$gray-3: #e5e6eb;
$gray-4: #dcdee0;
$gray-5: #c8c9cc;
$gray-6: #969799;
$gray-7: #646566;
$gray-8: #323233;
$black: #000000;

// Dark 主题色阶
$dark1: #1a1a1a;
$dark2: #2c2c2c;
$dark3: #3d3d3d;
$dark4: #4f4f4f;
$dark5: #606060;
$dark6: #717171;
$dark7: #828282;
$dark8: #939393;

// 功能色
$red: #fa2c19;
$red-dark: #e02d1b;
$orange: #ff8f1f;
$orange-dark: #e6741a;
$yellow: #ffc82c;
$yellow-dark: #e6b329;
$green: #64d572;
$green-dark: #5ac066;
$blue: #3460fa;
$blue-dark: #2e56e0;
$purple: #8b5cf6;
$purple-dark: #7c3aed;

// 动画变量
$animation-duration-base: 0.3s;
$animation-duration-fast: 0.2s;
$animation-duration-slow: 0.5s;
$animation-timing-fun: ease;
$animation-timing-fun-enter: ease-out;
$animation-timing-fun-leave: ease-in;

// 边框
$border-width-base: 1px;
$border-style-base: solid;
$border-color-base: $border-color;
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-max: 999px;

// 字体
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;
$font-weight-normal: 400;
$font-weight-bold: 500;
$line-height-xs: 14px;
$line-height-sm: 18px;
$line-height-md: 20px;
$line-height-lg: 22px;

// 间距
$padding-base: 4px;
$padding-xs: $padding-base * 2;
$padding-sm: $padding-base * 3;
$padding-md: $padding-base * 4;
$padding-lg: $padding-base * 6;
$padding-xl: $padding-base * 8;

$margin-base: 4px;
$margin-xs: $margin-base * 2;
$margin-sm: $margin-base * 3;
$margin-md: $margin-base * 4;
$margin-lg: $margin-base * 6;
$margin-xl: $margin-base * 8;

// 组件特定变量
$cell-background-color: $white;
$cell-border-color: $border-color;
$cell-padding-vertical: 10px;
$cell-padding-horizontal: 16px;
$cell-font-size: $font-size-md;
$cell-line-height: $line-height-md;

$button-border-radius: $border-radius-md;
$button-font-size: $font-size-md;
$button-line-height: $line-height-md;
$button-padding-vertical: 8px;
$button-padding-horizontal: 16px;
