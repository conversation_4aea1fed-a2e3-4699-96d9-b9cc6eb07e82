// 导入依赖服务用于内部使用
import { api as API } from './api'
import { StorageService as Storage } from './storage'
import { Utils } from './utils'

// 服务层统一导出
export { api, default as API } from './api'
export { StorageService, default as Storage } from './storage'
export { Utils, default as Util } from './utils'
export { ShareService, default as Share } from './share'

// 业务服务类
export class RecycleService {
  // 获取回收分类
  static async getCategories() {
    try {
      const categories = await API.category.getAll()
      return categories
    } catch (error) {
      console.error('获取分类失败:', error)
      return []
    }
  }

  // 搜索商品
  static async searchProducts(keyword: string) {
    try {
      // 添加到搜索历史
      Storage.addSearchHistory(keyword)

      const products = await API.category.search(keyword)
      return products
    } catch (error) {
      console.error('搜索失败:', error)
      return []
    }
  }

  // 获取商品估价
  static async getProductValuation(data: {
    productId: string
    condition: string
    accessories: string[]
  }) {
    try {
      Utils.showLoading('估价中...')
      const result = await API.valuation.getPrice(data)
      Utils.hideLoading()
      return result
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('估价失败，请重试')
      throw error
    }
  }

  // 创建回收订单
  static async createOrder(orderData: any) {
    try {
      Utils.showLoading('创建订单中...')
      const order = await API.order.create(orderData)
      Utils.hideLoading()
      Utils.showSuccess('订单创建成功')
      return order
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('订单创建失败')
      throw error
    }
  }

  // 获取用户订单列表
  static async getUserOrders(status?: string) {
    try {
      const orders = await API.order.getList(status)
      return orders
    } catch (error) {
      console.error('获取订单失败:', error)
      return []
    }
  }
}

// 用户服务类
export class UserService {
  // 用户登录
  static async login(phone: string, code: string) {
    try {
      Utils.showLoading('登录中...')
      const result = await API.user.login({ phone, code })

      // 保存用户信息和token
      if (result.token) {
        Storage.setToken(result.token)
      }
      if (result.userInfo) {
        Storage.setUserInfo(result.userInfo)
      }

      Utils.hideLoading()
      Utils.showSuccess('登录成功')
      return result
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('登录失败')
      throw error
    }
  }

  // 发送验证码
  static async sendVerificationCode(phone: string) {
    try {
      if (!Utils.validatePhone(phone)) {
        Utils.showError('请输入正确的手机号')
        return false
      }

      Utils.showLoading('发送中...')
      await API.user.sendCode(phone)
      Utils.hideLoading()
      Utils.showSuccess('验证码已发送')
      return true
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('发送失败，请重试')
      return false
    }
  }

  // 获取用户信息
  static getUserInfo() {
    try {
      return Storage.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  // 检查登录状态
  static isLoggedIn() {
    try {
      const token = Storage.getToken()
      const userInfo = Storage.getUserInfo()
      return !!(token && userInfo)
    } catch (error) {
      console.error('检查登录状态失败:', error)
      return false
    }
  }

  // 退出登录
  static logout() {
    try {
      Storage.clearToken()
      Storage.clearUserInfo()
      Utils.showSuccess('已退出登录')
    } catch (error) {
      console.error('退出登录失败:', error)
      Utils.showError('退出登录失败')
    }
  }

  // 更新用户信息
  static async updateProfile(data: any) {
    try {
      Utils.showLoading('更新中...')
      const result = await API.user.updateProfile(data)

      // 更新本地存储
      const currentUserInfo = Storage.getUserInfo()
      Storage.setUserInfo({ ...currentUserInfo, ...data })

      Utils.hideLoading()
      Utils.showSuccess('更新成功')
      return result
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('更新失败')
      throw error
    }
  }
}

// 排行榜服务类
export class RankingService {
  // 获取涨价榜
  static async getRisingRanking() {
    try {
      const data = await API.ranking.getRising()
      return data
    } catch (error) {
      console.error('获取涨价榜失败:', error)
      return []
    }
  }

  // 获取降价榜
  static async getFallingRanking() {
    try {
      const data = await API.ranking.getFalling()
      return data
    } catch (error) {
      console.error('获取降价榜失败:', error)
      return []
    }
  }

  // 获取热门榜
  static async getHotRanking() {
    try {
      const data = await API.ranking.getHot()
      return data
    } catch (error) {
      console.error('获取热门榜失败:', error)
      return []
    }
  }
}

// 地址服务类
export class AddressService {
  // 获取用户地址列表
  static async getAddressList() {
    try {
      const addresses = await API.address.getList()
      return addresses
    } catch (error) {
      console.error('获取地址失败:', error)
      return []
    }
  }

  // 添加地址
  static async addAddress(addressData: any) {
    try {
      Utils.showLoading('添加中...')
      const result = await API.address.add(addressData)
      Utils.hideLoading()
      Utils.showSuccess('地址添加成功')
      return result
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('添加失败')
      throw error
    }
  }

  // 更新地址
  static async updateAddress(id: string, addressData: any) {
    try {
      Utils.showLoading('更新中...')
      const result = await API.address.update(id, addressData)
      Utils.hideLoading()
      Utils.showSuccess('地址更新成功')
      return result
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('更新失败')
      throw error
    }
  }

  // 删除地址
  static async deleteAddress(id: string) {
    try {
      const confirmed = await Utils.showConfirm('确定要删除这个地址吗？')
      if (!confirmed) return false

      Utils.showLoading('删除中...')
      await API.address.delete(id)
      Utils.hideLoading()
      Utils.showSuccess('地址删除成功')
      return true
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('删除失败')
      return false
    }
  }

  // 设置默认地址
  static async setDefaultAddress(id: string) {
    try {
      Utils.showLoading('设置中...')
      await API.address.setDefault(id)
      Utils.hideLoading()
      Utils.showSuccess('默认地址设置成功')
      return true
    } catch (error) {
      Utils.hideLoading()
      Utils.showError('设置失败')
      return false
    }
  }
}
