import Taro from '@tarojs/taro'

// 分享服务类
export class ShareService {
  // 默认分享配置
  private static defaultShareConfig = {
    title: '暴风回收 - 专业手机回收平台',
    path: '/pages/index/index',
    imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
  }

  // 获取分享配置
  static getShareConfig(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    return {
      ...this.defaultShareConfig,
      ...options
    }
  }

  // 显示分享菜单
  static showShareMenu(options?: {
    withShareTicket?: boolean
  }) {
    const defaultOptions = {
      withShareTicket: true
    }

    const shareMenuOptions = { ...defaultOptions, ...options }

    if (process.env.TARO_ENV === 'weapp') {
      Taro.showShareMenu(shareMenuOptions)
    }
  }

  // 隐藏分享菜单
  static hideShareMenu() {
    if (process.env.TARO_ENV === 'weapp') {
      Taro.hideShareMenu()
    }
  }

  // 分享给微信好友
  static shareToFriend(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const shareConfig = this.getShareConfig(options)

    if (process.env.TARO_ENV === 'weapp') {
      // 显示分享菜单
      this.showShareMenu({
        withShareTicket: true
      })

      Taro.showToast({
        title: '请点击右上角分享给好友',
        icon: 'none',
        duration: 2000
      })
    } else {
      Taro.showToast({
        title: '请在微信中打开小程序进行分享',
        icon: 'none',
        duration: 2000
      })
    }

    return shareConfig
  }

  // 分享到朋友圈
  static shareToMoment(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const shareConfig = this.getShareConfig(options)

    if (process.env.TARO_ENV === 'weapp') {
      // 显示分享菜单
      this.showShareMenu({
        withShareTicket: true
      })

      Taro.showToast({
        title: '请点击右上角分享到朋友圈',
        icon: 'none',
        duration: 2000
      })
    } else {
      Taro.showToast({
        title: '请在微信中打开小程序进行分享',
        icon: 'none',
        duration: 2000
      })
    }

    return shareConfig
  }

  // 显示分享选项
  static showShareOptions(callback?: (type: 'friend' | 'moment' | 'copy') => void) {
    Taro.showActionSheet({
      itemList: ['分享给微信好友', '分享到朋友圈', '复制小程序链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            callback?.('friend')
            break
          case 1:
            callback?.('moment')
            break
          case 2:
            callback?.('copy')
            break
        }
      }
    })
  }

  // 复制小程序链接
  static async copyMiniProgramLink(link?: string) {
    const defaultLink = 'https://www.baofengrecycle.com/miniprogram'
    const copyLink = link || defaultLink

    try {
      await Taro.setClipboardData({
        data: copyLink
      })
      Taro.showToast({
        title: '小程序链接已复制',
        icon: 'success',
        duration: 2000
      })
    } catch (error) {
      console.error('复制失败:', error)
      Taro.showToast({
        title: '复制失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  }

  // 一键分享 - 显示所有分享选项
  static oneClickShare(options?: {
    title?: string
    path?: string
    imageUrl?: string
    link?: string
  }) {
    this.showShareOptions((type) => {
      switch (type) {
        case 'friend':
          this.shareToFriend(options)
          break
        case 'moment':
          this.shareToMoment(options)
          break
        case 'copy':
          this.copyMiniProgramLink(options?.link)
          break
      }
    })
  }

  // 获取分享统计信息（如果需要）
  static getShareInfo() {
    // 这里可以添加分享统计逻辑
    return {
      shareCount: 0,
      lastShareTime: null
    }
  }

  // 设置页面分享配置
  static setupPageShare(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const shareConfig = this.getShareConfig(options)

    // 返回分享配置，用于页面的 useShareAppMessage
    return {
      onShareAppMessage: () => shareConfig,
      onShareTimeline: () => ({
        title: shareConfig.title,
        imageUrl: shareConfig.imageUrl
      })
    }
  }
}

export default ShareService
