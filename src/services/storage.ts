import Taro from '@tarojs/taro'

// 本地存储服务
export class StorageService {
  // 存储用户信息
  static setUserInfo(userInfo: any) {
    return Taro.setStorageSync('userInfo', userInfo)
  }

  // 获取用户信息
  static getUserInfo() {
    try {
      return Taro.getStorageSync('userInfo')
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  // 清除用户信息
  static clearUserInfo() {
    return Taro.removeStorageSync('userInfo')
  }

  // 存储登录token
  static setToken(token: string) {
    return Taro.setStorageSync('token', token)
  }

  // 获取登录token
  static getToken() {
    try {
      return Taro.getStorageSync('token')
    } catch (error) {
      console.error('获取token失败:', error)
      return null
    }
  }

  // 清除token
  static clearToken() {
    return Taro.removeStorageSync('token')
  }

  // 存储搜索历史
  static setSearchHistory(history: string[]) {
    return Taro.setStorageSync('searchHistory', history)
  }

  // 获取搜索历史
  static getSearchHistory(): string[] {
    try {
      return Taro.getStorageSync('searchHistory') || []
    } catch (error) {
      console.error('获取搜索历史失败:', error)
      return []
    }
  }

  // 添加搜索记录
  static addSearchHistory(keyword: string) {
    const history = this.getSearchHistory()
    const newHistory = [keyword, ...history.filter(item => item !== keyword)].slice(0, 10)
    this.setSearchHistory(newHistory)
  }

  // 清除搜索历史
  static clearSearchHistory() {
    return Taro.removeStorageSync('searchHistory')
  }

  // 存储用户设置
  static setSettings(settings: any) {
    return Taro.setStorageSync('settings', settings)
  }

  // 获取用户设置
  static getSettings() {
    try {
      return Taro.getStorageSync('settings') || {
        notifications: true,
        language: 'zh-CN',
        theme: 'light'
      }
    } catch (error) {
      console.error('获取设置失败:', error)
      return {
        notifications: true,
        language: 'zh-CN',
        theme: 'light'
      }
    }
  }

  // 存储购物车数据
  static setCart(cart: any[]) {
    return Taro.setStorageSync('cart', cart)
  }

  // 获取购物车数据
  static getCart(): any[] {
    try {
      return Taro.getStorageSync('cart') || []
    } catch (error) {
      console.error('获取购物车失败:', error)
      return []
    }
  }

  // 清除购物车
  static clearCart() {
    return Taro.removeStorageSync('cart')
  }

  // 通用存储方法
  static set(key: string, value: any) {
    return Taro.setStorageSync(key, value)
  }

  // 通用获取方法
  static get(key: string) {
    try {
      return Taro.getStorageSync(key)
    } catch (error) {
      console.error(`获取${key}失败:`, error)
      return null
    }
  }

  // 通用删除方法
  static remove(key: string) {
    return Taro.removeStorageSync(key)
  }

  // 清除所有存储
  static clear() {
    return Taro.clearStorageSync()
  }
}

export default StorageService
