import Taro from '@tarojs/taro'
import { api } from './api'

// 用户信息接口
export interface UserInfo {
  openid?: string
  unionid?: string
  session_key?: string
  code?: string
  loginTime?: string
  nickName?: string
  avatarUrl?: string
  phone?: string
  accessToken?: string  // JWT访问令牌
  [key: string]: any
}

// 用户服务类
export class UserService {
  private static readonly USER_INFO_KEY = 'userInfo'

  /**
   * 执行微信登录
   * @returns Promise<UserInfo>
   */
  static async wxLogin(): Promise<UserInfo> {
    try {
      const loginResult = await Taro.login()
      console.log('微信登录成功，获取到 code:', loginResult.code)

      // 将 code 发送到后端服务器换取 openid 和 session_key
      try {
        const userData = await api.user.wxLogin({ code: loginResult.code })
        console.log('后端登录成功，用户信息:', userData)

        // 保存用户信息
        this.saveUserInfo(userData)


        return userData
      } catch (apiError) {
        console.log('后端登录接口调用失败，但微信登录成功:', apiError)
        // 即使后端接口失败，也保存基本信息
        const basicUserInfo: UserInfo = {
          code: loginResult.code,
          loginTime: new Date().toISOString()
        }
        this.saveUserInfo(basicUserInfo)
        return basicUserInfo
      }

    } catch (error) {
      console.error('微信登录失败:', error)
      Taro.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      })
      throw error
    }
  }

  /**
   * 获取本地存储的用户信息
   * @returns UserInfo | null
   */
  static getUserInfo(): UserInfo | null {
    try {
      const userInfo = Taro.getStorageSync(this.USER_INFO_KEY)
      return userInfo || null
    } catch (error) {
      console.log('读取本地用户信息失败:', error)
      return null
    }
  }

  /**
   * 保存用户信息到本地存储
   * @param userInfo 用户信息
   */
  static saveUserInfo(userInfo: UserInfo): void {
    try {
      Taro.setStorageSync(this.USER_INFO_KEY, userInfo)
      console.log('用户信息已保存到本地存储')
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  /**
   * 获取微信小程序用户手机号
   * @param code 手机号授权码
   * @returns Promise<UserInfo>
   */
  static async getPhoneNumber(code: string): Promise<UserInfo> {
    try {
      console.log('开始获取用户手机号，code:', code)

      // 调用后端接口获取手机号
      const response = await api.user.getPhoneNumber({ code })
      console.log('获取手机号接口响应:', response)

      // 处理后端响应数据
      let userData: UserInfo
      if (response.success && response.data) {
        // 标准响应格式
        userData = response.data
      } else if (response.data) {
        // 直接返回数据格式
        userData = response
      } else {
        throw new Error('后端返回数据格式错误')
      }

      // 合并现有用户信息
      const existingUserInfo = this.getUserInfo() || {}
      const mergedUserInfo = {
        ...existingUserInfo,
        ...userData,
        loginTime: new Date().toISOString()
      }

      // 保存合并后的用户信息
      this.saveUserInfo(mergedUserInfo)
      console.log('用户信息已更新并保存:', mergedUserInfo)

      return mergedUserInfo
    } catch (error) {
      console.error('获取手机号失败:', error)
      Taro.showToast({
        title: '获取手机号失败',
        icon: 'none'
      })
      throw error
    }
  }

  /**
   * 检查用户是否已授权手机号
   * @returns boolean
   */
  static hasPhoneAuthorization(): boolean {
    const userInfo = this.getUserInfo()
    return userInfo !== null && !!userInfo.phone
  }

  /**
   * 清除用户信息
   */
  static clearUserInfo(): void {
    try {
      Taro.removeStorageSync(this.USER_INFO_KEY)
      console.log('用户信息已清除')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  }

  /**
   * 检查用户是否已登录
   * @returns boolean
   */
  static isLoggedIn(): boolean {
    const userInfo = this.getUserInfo()
    return userInfo !== null && !!(userInfo.openid || userInfo.code || userInfo.accessToken)
  }

  /**
   * 获取用户的访问令牌
   * @returns string | null
   */
  static getAccessToken(): string | null {
    const userInfo = this.getUserInfo()
    return userInfo?.accessToken || null
  }

  /**
   * 获取用户显示名称
   * @returns string
   */
  static getUserDisplayName(): string {
    const userInfo = this.getUserInfo()
    if (!userInfo) return '未登录'

    if (userInfo.nickName) return userInfo.nickName
    if (userInfo.openid) return `用户${userInfo.openid.substring(0, 8)}`
    return '微信用户'
  }

  /**
   * 自动登录（检查本地信息，如果没有则执行微信登录）
   * @returns Promise<UserInfo>
   */
  static async autoLogin(): Promise<UserInfo> {
    const localUserInfo = this.getUserInfo()

    if (localUserInfo) {
      console.log('发现本地用户信息，使用本地登录:', localUserInfo)
      return localUserInfo
    }

    console.log('没有本地用户信息，开始微信登录')
    return await this.wxLogin()
  }
}

export default UserService
