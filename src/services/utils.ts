import Taro from '@tarojs/taro'

// 工具函数集合
export class Utils {
  // 格式化价格
  static formatPrice(price: number): string {
    return `¥${price.toFixed(2)}`
  }

  // 格式化日期
  static formatDate(date: Date | string, format: string = 'YYYY-MM-DD'): string {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  }

  // 手机号脱敏
  static maskPhone(phone: string): string {
    if (!phone || phone.length !== 11) return phone
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  // 验证手机号
  static validatePhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  // 验证邮箱
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // 防抖函数
  static debounce(func: Function, wait: number) {
    let timeout: NodeJS.Timeout
    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // 节流函数
  static throttle(func: Function, limit: number) {
    let inThrottle: boolean
    return function executedFunction(...args: any[]) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  // 深拷贝
  static deepClone(obj: any): any {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => this.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  }

  // 生成随机字符串
  static generateRandomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  // 获取文件扩展名
  static getFileExtension(filename: string): string {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
  }

  // 计算文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 显示加载提示
  static showLoading(title: string = '加载中...') {
    Taro.showLoading({ title, mask: true })
  }

  // 隐藏加载提示
  static hideLoading() {
    Taro.hideLoading()
  }

  // 显示成功提示
  static showSuccess(title: string) {
    Taro.showToast({
      title,
      icon: 'success',
      duration: 2000
    })
  }

  // 显示错误提示
  static showError(title: string) {
    Taro.showToast({
      title,
      icon: 'none',
      duration: 2000
    })
  }

  // 显示确认对话框
  static showConfirm(content: string, title: string = '提示'): Promise<boolean> {
    return new Promise((resolve) => {
      Taro.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }

  // 复制到剪贴板
  static copyToClipboard(text: string): Promise<void> {
    return Taro.setClipboardData({
      data: text
    }).then(() => {
      this.showSuccess('复制成功')
    }).catch(() => {
      this.showError('复制失败')
    })
  }

  // 拨打电话
  static makePhoneCall(phoneNumber: string) {
    Taro.makePhoneCall({
      phoneNumber
    }).catch(() => {
      this.showError('拨打电话失败')
    })
  }

  // 预览图片
  static previewImage(urls: string[], current: string = '') {
    Taro.previewImage({
      urls,
      current: current || urls[0]
    })
  }

  // 选择图片
  static chooseImage(count: number = 1): Promise<string[]> {
    return Taro.chooseImage({
      count,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera']
    }).then(res => res.tempFilePaths)
  }

  // 获取系统信息
  static getSystemInfo() {
    return Taro.getSystemInfoSync()
  }

  // 获取网络状态
  static getNetworkType(): Promise<string> {
    return Taro.getNetworkType().then(res => res.networkType)
  }

  // 显示普通提示
  static showToast(title: string) {
    Taro.showToast({
      title,
      icon: 'none',
      duration: 2000
    })
  }

  // 分享功能 - 直接调用微信分享
  static shareApp(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const defaultOptions = {
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index',
      imageUrl: ''
    }

    const shareOptions = { ...defaultOptions, ...options }

    // 直接调用微信分享API
    if (process.env.TARO_ENV === 'weapp') {
      // 微信小程序环境
      Taro.showShareMenu({
        withShareTicket: true
      })
      this.showToast('请点击右上角分享按钮')
    } else {
      // 其他环境的分享处理
      this.showActionSheet(['分享给微信好友', '分享到朋友圈', '复制链接'], (index) => {
        switch (index) {
          case 0:
            this.showToast('请在微信中打开小程序进行分享')
            break
          case 1:
            this.showToast('请在微信中打开小程序进行分享')
            break
          case 2:
            this.copyToClipboard('https://www.baofengrecycle.com')
            break
        }
      })
    }

    return shareOptions
  }

  // 显示操作表
  static showActionSheet(itemList: string[], callback: (index: number) => void) {
    Taro.showActionSheet({
      itemList,
      success: (res) => {
        callback(res.tapIndex)
      }
    })
  }

  // 更新分享内容
  static updateShareContent(options: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    return {
      title: options.title || '暴风回收 - 专业手机回收平台',
      path: options.path || '/pages/index/index',
      imageUrl: options.imageUrl || ''
    }
  }

  // 分享到微信好友
  static shareToFriend(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const shareData = this.updateShareContent(options || {})

    if (process.env.TARO_ENV === 'weapp') {
      // 在微信小程序中，通过显示分享菜单来实现
      Taro.showShareMenu({
        withShareTicket: true
      })
      this.showToast('请点击右上角分享给好友')
    } else {
      this.showToast('请在微信中打开小程序进行分享')
    }

    return shareData
  }

  // 分享到朋友圈
  static shareToMoment(options?: {
    title?: string
    path?: string
    imageUrl?: string
  }) {
    const shareData = this.updateShareContent(options || {})

    if (process.env.TARO_ENV === 'weapp') {
      // 在微信小程序中，通过显示分享菜单来实现
      Taro.showShareMenu({
        withShareTicket: true
      })
      this.showToast('请点击右上角分享到朋友圈')
    } else {
      this.showToast('请在微信中打开小程序进行分享')
    }

    return shareData
  }
}

export default Utils
