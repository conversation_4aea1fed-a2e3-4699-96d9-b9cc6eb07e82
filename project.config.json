{"miniprogramRoot": "dist/", "projectname": "baofeng-miniprogram", "description": "暴风回收", "appid": "touristappid", "setting": {"urlCheck": false, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}}