{"name": "baofeng-miniprogram", "version": "1.0.0", "private": true, "description": "暴风回收", "templateInfo": {"name": "react-NutUI", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type swan", "build:alipay": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type alipay", "build:tt": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type rn", "build:qq": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type qq", "build:jd": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type jd", "build:quickapp": "cross-env NODE_OPTIONS=\"--openssl-legacy-provider\" taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.21.5", "@nutui/nutui-react-taro": "^1.4.8", "@tarojs/components": "3.6.34", "@tarojs/helper": "3.6.34", "@tarojs/mini-runner": "^3.6.37", "@tarojs/plugin-framework-react": "3.6.34", "@tarojs/plugin-html": "3.6.34", "@tarojs/plugin-platform-alipay": "3.6.34", "@tarojs/plugin-platform-h5": "3.6.34", "@tarojs/plugin-platform-jd": "3.6.34", "@tarojs/plugin-platform-qq": "3.6.34", "@tarojs/plugin-platform-swan": "3.6.34", "@tarojs/plugin-platform-tt": "3.6.34", "@tarojs/plugin-platform-weapp": "3.6.34", "@tarojs/react": "3.6.34", "@tarojs/runtime": "3.6.34", "@tarojs/shared": "3.6.34", "@tarojs/taro": "3.6.34", "@tarojs/webpack5-runner": "^4.1.3-alpha.0", "react": "^17.0.0", "react-dom": "^17.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-react": "^7.24.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@tarojs/cli": "3.6.34", "@tarojs/webpack-runner": "3.6.34", "@types/react": "^17.0.87", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-plugin-import": "^1.13.8", "babel-preset-taro": "3.6.34", "cross-env": "^7.0.3", "eslint": "^8.12.0", "eslint-config-taro": "3.6.34", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "postcss": "^8.4.18", "sass": "^1.60.0", "stylelint": "^14.4.0", "terser": "^5.16.8", "typescript": "^4.9.5"}}