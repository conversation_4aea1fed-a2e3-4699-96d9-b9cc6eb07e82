# 手机号授权功能实现说明

## 功能概述

实现了在用户点击底部导航栏"我的"页面时，要求用户授权手机号的功能。只有授权手机号后，用户才能正常访问"我的"页面的完整功能。

## 实现的功能

### 1. 用户状态检查
- 页面渲染完成后自动检查用户登录状态和手机号授权状态
- 区分三种状态：
  - 未登录：显示登录按钮
  - 已登录但未授权手机号：显示手机号授权按钮
  - 已登录且已授权手机号：显示完整用户信息和功能

### 2. 手机号授权流程
- 使用微信小程序的 `getPhoneNumber` API 获取用户手机号
- 调用后端接口 `/api/v1/wechat/phone` 解密获取真实手机号
- 授权成功后保存用户信息到本地存储
- 授权失败时显示错误提示

### 3. 界面适配
- 未授权用户只能看到授权提示，无法访问联系功能
- 已授权用户可以看到完整的联系我们功能（电话、微信、分享）
- 动态显示按钮文本（"点击登录" / "授权手机号"）

## 修改的文件

### 1. `src/services/user.ts`
- 修复了 `saveUserInfo` 方法的实现错误
- 新增 `getPhoneNumber` 方法调用后端手机号授权接口
- 新增 `hasPhoneAuthorization` 方法检查用户是否已授权手机号
- 修复了 `isLoggedIn` 方法的类型错误

### 2. `src/services/api.ts`
- 新增 `getPhoneNumber` 接口，调用 `/api/v1/wechat/phone`
- 改进请求拦截器，自动添加 Authorization 头
- 支持Bearer Token认证

### 3. `src/pages/profile/index.tsx`
- 重构页面状态管理，新增 `userInfo` 和 `showPhoneAuth` 状态
- 实现 `checkUserAuthStatus` 方法检查用户授权状态
- 重写 `handleGetPhoneNumber` 方法，调用真实的后端接口
- 修改页面渲染逻辑，根据授权状态显示不同内容
- 未授权用户无法看到联系功能，只显示授权提示

### 4. `src/pages/profile/index.scss`
- 新增 `.auth-prompt` 样式，用于显示授权提示
- 改进 `.login-btn-large` 样式，统一按钮外观

## API 接口规范

### 手机号授权接口
```
POST /api/v1/wechat/phone
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
  "code": "phone_auth_code_from_frontend"
}

Response:
{
  "success": true,
  "message": "手机号获取成功",
  "data": {
    "openid": "user_openid",
    "phone": "13800138000",
    "nickname": "用户昵称",
    "avatar": "头像URL"
  }
}
```

## 用户体验流程

1. **用户点击"我的"页面**
   - 系统自动检查登录状态和手机号授权状态

2. **未授权用户**
   - 看到"授权手机号"按钮和提示文本
   - 点击按钮触发微信手机号授权弹窗
   - 用户同意后调用后端接口获取手机号
   - 授权成功后显示完整页面功能

3. **已授权用户**
   - 直接显示用户信息和完整功能
   - 可以使用电话联系、复制微信号、分享等功能

4. **授权失败处理**
   - 用户拒绝授权：显示提示并在2秒后跳转到首页
   - 接口调用失败：显示错误提示，允许重试

## 安全考虑

- 所有API请求自动携带Bearer Token
- 手机号授权需要用户主动同意
- 敏感功能只对已授权用户开放
- 本地存储用户信息，避免重复授权

## 测试建议

1. 测试未登录用户访问"我的"页面
2. 测试已登录但未授权手机号的用户
3. 测试手机号授权成功和失败的情况
4. 测试用户拒绝授权的处理
5. 测试网络异常时的错误处理
