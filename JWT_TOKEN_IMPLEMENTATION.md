# JWT Token 实现说明

## 功能概述

实现了从本地存储的 `userInfo` 中获取 `accessToken` 并在API请求中传递进行JWT校验的功能。

## 实现的功能

### 1. JWT Token 获取机制
- 优先从 `userInfo.accessToken` 字段获取JWT令牌
- 兼容旧版本，从 `token` 字段获取令牌
- 自动在所有API请求中添加 `Authorization: Bearer {token}` 头

### 2. JWT Token 校验
- 所有API请求自动携带JWT令牌
- 处理401未授权响应，自动清除过期令牌
- 显示友好的重新登录提示

### 3. 用户信息管理
- 支持 `accessToken` 字段的用户信息接口
- 合并现有用户信息和新获取的信息
- 提供获取访问令牌的便捷方法

## 修改的文件

### 1. `src/services/api.ts`
- 新增 `getAccessToken()` 辅助函数，优先从 `userInfo.accessToken` 获取令牌
- 改进请求拦截器，自动添加JWT Authorization头
- 新增401错误处理，自动清除过期令牌
- 完整的API接口定义

### 2. `src/services/user.ts`
- 更新 `UserInfo` 接口，新增 `accessToken` 字段
- 改进 `getPhoneNumber` 方法，正确处理后端响应数据
- 新增 `getAccessToken()` 方法获取用户访问令牌
- 改进登录状态检查，支持 `accessToken` 验证

## JWT Token 流程

### 1. 令牌获取优先级
```typescript
// 1. 优先从 userInfo.accessToken 获取
const userInfo = Taro.getStorageSync('userInfo')
if (userInfo && userInfo.accessToken) {
  return userInfo.accessToken
}

// 2. 兼容旧版本，从 token 字段获取
const token = Taro.getStorageSync('token')
if (token) {
  return token
}

// 3. 没有令牌
return null
```

### 2. API请求流程
```typescript
// 自动添加Authorization头
const requestHeader = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${accessToken}` // 如果有令牌
}
```

### 3. 错误处理流程
```typescript
// 401 未授权处理
if (statusCode === 401) {
  // 清除本地存储
  Taro.removeStorageSync('userInfo')
  Taro.removeStorageSync('token')
  
  // 显示重新登录提示
  Taro.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none'
  })
}
```

## 存储结构

### userInfo 存储格式
```json
{
  "openid": "wx_openid_string",
  "unionid": "wx_unionid_string", 
  "phone": "13800138000",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // JWT令牌
  "loginTime": "2025-06-21T10:08:31.000Z"
}
```

## API接口规范

### 手机号授权接口
```
POST /api/v1/wechat/phone
Authorization: Bearer {accessToken}
Content-Type: application/json

Request Body:
{
  "code": "phone_auth_code_from_frontend"
}

Response (成功):
{
  "success": true,
  "message": "手机号获取成功",
  "data": {
    "openid": "user_openid",
    "phone": "13800138000",
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "accessToken": "new_jwt_token_if_refreshed"
  }
}

Response (JWT过期 - 401):
{
  "success": false,
  "message": "JWT令牌无效或已过期",
  "code": 401
}
```

## 安全特性

### 1. 自动令牌管理
- 所有API请求自动携带JWT令牌
- 无需手动管理Authorization头
- 支持令牌刷新机制

### 2. 过期处理
- 自动检测401未授权响应
- 清除过期的本地令牌
- 友好的用户提示

### 3. 兼容性
- 向后兼容旧的token存储方式
- 渐进式升级到新的userInfo.accessToken方式

## 测试建议

### 1. 正常流程测试
- 用户授权手机号后检查 `userInfo.accessToken` 是否正确保存
- 后续API请求是否自动携带JWT令牌
- 令牌有效时API调用是否正常

### 2. 令牌过期测试
- 模拟后端返回401响应
- 检查本地存储是否被清除
- 用户是否收到重新登录提示

### 3. 兼容性测试
- 测试只有旧版token字段的情况
- 测试同时有token和userInfo.accessToken的情况
- 测试令牌获取的优先级

## 调试信息

在开发环境中，可以通过以下方式查看JWT令牌：

```javascript
// 在浏览器控制台或小程序调试器中执行
const userInfo = wx.getStorageSync('userInfo')
console.log('用户信息:', userInfo)
console.log('JWT令牌:', userInfo?.accessToken)

// 检查令牌是否有效
const token = userInfo?.accessToken
if (token) {
  const payload = JSON.parse(atob(token.split('.')[1]))
  console.log('JWT载荷:', payload)
  console.log('过期时间:', new Date(payload.exp * 1000))
}
```
