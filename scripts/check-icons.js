/**
 * 检查 tabbar 图标文件是否存在
 * 使用方法：node scripts/check-icons.js
 */

const fs = require('fs');
const path = require('path');

// 需要检查的图标文件
const requiredIcons = [
  'home.png',
  'home-active.png',
  'category.png',
  'category-active.png',
  'ranking.png',
  'ranking-active.png',
  'profile.png',
  'profile-active.png'
];

const iconsDir = path.join(__dirname, '../src/assets/icons');

console.log('🔍 检查 tabbar 图标文件...\n');

let allExists = true;

requiredIcons.forEach(iconFile => {
  const iconPath = path.join(iconsDir, iconFile);
  const exists = fs.existsSync(iconPath);
  
  if (exists) {
    const stats = fs.statSync(iconPath);
    const sizeKB = (stats.size / 1024).toFixed(2);
    console.log(`✅ ${iconFile} - ${sizeKB}KB`);
  } else {
    console.log(`❌ ${iconFile} - 文件不存在`);
    allExists = false;
  }
});

console.log('\n📋 配置检查:');

// 检查 app.config.ts 中的配置
const appConfigPath = path.join(__dirname, '../src/app.config.ts');
if (fs.existsSync(appConfigPath)) {
  const configContent = fs.readFileSync(appConfigPath, 'utf8');
  
  if (configContent.includes('iconPath') && configContent.includes('selectedIconPath')) {
    console.log('✅ app.config.ts 中已配置图标路径');
  } else {
    console.log('❌ app.config.ts 中缺少图标路径配置');
    allExists = false;
  }
} else {
  console.log('❌ 找不到 app.config.ts 文件');
  allExists = false;
}

// 检查构建配置
const buildConfigPath = path.join(__dirname, '../config/index.ts');
if (fs.existsSync(buildConfigPath)) {
  const buildConfigContent = fs.readFileSync(buildConfigPath, 'utf8');
  
  if (buildConfigContent.includes('src/assets/')) {
    console.log('✅ 构建配置中已添加静态资源复制');
  } else {
    console.log('⚠️  构建配置中可能缺少静态资源复制配置');
  }
}

console.log('\n' + '='.repeat(50));

if (allExists) {
  console.log('🎉 所有图标文件检查通过！');
  console.log('\n📝 接下来的步骤：');
  console.log('1. 运行 pnpm run dev:weapp 重新编译项目');
  console.log('2. 在微信开发者工具中查看底部导航栏效果');
  console.log('3. 如果图标仍然不显示，请检查微信开发者工具的控制台错误信息');
} else {
  console.log('❌ 存在问题，请按照上述提示修复');
  console.log('\n💡 常见解决方案：');
  console.log('1. 确保所有图标文件都存在于 src/assets/icons/ 目录');
  console.log('2. 检查图标文件名是否与配置中的完全一致');
  console.log('3. 确保图标文件格式为 PNG 且尺寸合适（推荐 78x78px）');
  console.log('4. 重新编译项目并刷新微信开发者工具');
}
