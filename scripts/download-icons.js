/**
 * 下载 tabbar 图标的脚本
 * 使用方法：node scripts/download-icons.js
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// 图标配置
const icons = [
  {
    name: 'home',
    normalUrl: 'https://img.icons8.com/ios/78/666666/home.png',
    activeUrl: 'https://img.icons8.com/ios-filled/78/1890ff/home.png'
  },
  {
    name: 'category',
    normalUrl: 'https://img.icons8.com/ios/78/666666/grid.png',
    activeUrl: 'https://img.icons8.com/ios-filled/78/1890ff/grid.png'
  },
  {
    name: 'ranking',
    normalUrl: 'https://img.icons8.com/ios/78/666666/bar-chart.png',
    activeUrl: 'https://img.icons8.com/ios-filled/78/1890ff/bar-chart.png'
  },
  {
    name: 'profile',
    normalUrl: 'https://img.icons8.com/ios/78/666666/user.png',
    activeUrl: 'https://img.icons8.com/ios-filled/78/1890ff/user.png'
  }
];

// 确保目录存在
const iconsDir = path.join(__dirname, '../src/assets/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// 下载图标函数
function downloadIcon(url, filename) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(path.join(iconsDir, filename));
    
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        response.pipe(file);
        file.on('finish', () => {
          file.close();
          console.log(`✅ 下载完成: ${filename}`);
          resolve();
        });
      } else {
        reject(new Error(`下载失败: ${response.statusCode}`));
      }
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// 主函数
async function downloadAllIcons() {
  console.log('🚀 开始下载 tabbar 图标...\n');
  
  try {
    for (const icon of icons) {
      console.log(`📥 下载 ${icon.name} 图标...`);
      
      // 下载普通状态图标
      await downloadIcon(icon.normalUrl, `${icon.name}.png`);
      
      // 下载选中状态图标
      await downloadIcon(icon.activeUrl, `${icon.name}-active.png`);
      
      console.log('');
    }
    
    console.log('🎉 所有图标下载完成！');
    console.log('\n📝 接下来的步骤：');
    console.log('1. 检查 src/assets/icons/ 目录下的图标文件');
    console.log('2. 运行 pnpm run dev:weapp 重新编译项目');
    console.log('3. 在微信开发者工具中查看底部导航栏效果');
    
  } catch (error) {
    console.error('❌ 下载失败:', error.message);
    console.log('\n💡 备选方案：');
    console.log('1. 手动从 https://icons8.com 下载图标');
    console.log('2. 使用其他免费图标库（如 iconfont.cn）');
    console.log('3. 自行设计图标');
  }
}

// 执行下载
downloadAllIcons();
