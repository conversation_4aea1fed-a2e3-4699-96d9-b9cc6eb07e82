# 暴风回收微信小程序

基于 Taro + React + TypeScript + NutUI 开发的回收服务微信小程序。

## 项目特性

- 🚀 **Taro 4.1.2** - 多端统一开发框架
- ⚛️ **React 18** + **TypeScript** - 现代化开发体验
- 🎨 **NutUI React Taro** - 京东风格的移动端组件库
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🔧 **Vite** - 快速的构建工具
- 📦 **pnpm** - 高效的包管理器

## 项目结构

```
baofeng-miniprogram/
├── src/
│   ├── pages/                 # 页面目录
│   │   ├── index/            # 首页
│   │   ├── category/         # 类目页
│   │   ├── ranking/          # 涨跌榜页
│   │   └── profile/          # 我的页面
│   ├── services/             # 服务层
│   │   ├── api.ts           # API 接口
│   │   ├── storage.ts       # 本地存储
│   │   ├── utils.ts         # 工具函数
│   │   └── index.ts         # 服务统一导出
│   ├── assets/              # 静态资源
│   │   └── icons/           # 图标资源
│   ├── app.config.ts        # 小程序全局配置
│   ├── app.scss            # 全局样式
│   └── app.ts              # 小程序入口
├── config/                  # 构建配置
├── dist/                   # 编译输出目录
├── package.json            # 项目依赖
└── project.config.json     # 微信小程序配置
```

## 功能模块

### 🏠 首页
- 搜索功能
- 轮播图展示
- 快捷入口
- 热门商品推荐
- 公告通知

### 📂 类目页
- 商品分类展示
- 热门回收商品
- 搜索功能
- 分类筛选

### 📈 涨跌榜
- 涨价榜单
- 降价榜单
- 热门榜单
- 实时价格变动

### 👤 我的页面
- 用户信息展示
- 订单管理
- 钱包功能
- 设置中心

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 7.0.0
- 微信开发者工具

### 安装依赖

```bash
cd baofeng-miniprogram
pnpm install
```

### 开发命令

```bash
# 微信小程序开发
pnpm run dev:weapp

# 支付宝小程序开发
pnpm run dev:alipay

# 字节跳动小程序开发
pnpm run dev:tt

# H5 开发
pnpm run dev:h5
```

### 构建命令

```bash
# 微信小程序构建
pnpm run build:weapp

# 支付宝小程序构建
pnpm run build:alipay

# 字节跳动小程序构建
pnpm run build:tt

# H5 构建
pnpm run build:h5
```

## 服务层使用

项目提供了完整的服务层封装，包括：

### API 服务
```typescript
import { API } from '@/services'

// 获取用户信息
const userInfo = await API.user.getProfile()

// 获取商品分类
const categories = await API.category.getAll()
```

### 业务服务
```typescript
import { RecycleService, UserService } from '@/services'

// 用户登录
await UserService.login(phone, code)

// 获取回收分类
const categories = await RecycleService.getCategories()
```

### 工具函数
```typescript
import { Utils } from '@/services'

// 格式化价格
const price = Utils.formatPrice(1234.56) // ¥1234.56

// 显示提示
Utils.showSuccess('操作成功')
Utils.showError('操作失败')
```

## 配置说明

### 底部导航配置
在 `src/app.config.ts` 中配置底部导航：

```typescript
tabBar: {
  color: '#666666',
  selectedColor: '#1890ff',
  backgroundColor: '#ffffff',
  list: [
    { pagePath: 'pages/index/index', text: '首页' },
    { pagePath: 'pages/category/index', text: '类目' },
    { pagePath: 'pages/ranking/index', text: '涨跌榜' },
    { pagePath: 'pages/profile/index', text: '我的' }
  ]
}
```

### API 配置
在 `src/services/api.ts` 中配置 API 基础地址：

```typescript
const BASE_URL = 'https://api.baofeng-recycle.com'
```

## 部署说明

1. 运行构建命令生成 `dist` 目录
2. 使用微信开发者工具打开项目根目录
3. 选择 `dist` 目录作为小程序代码目录
4. 配置小程序 AppID
5. 上传代码并提交审核

## 注意事项

1. **图标资源**: 需要在 `src/assets/icons/` 目录下添加底部导航图标
2. **API 接口**: 需要替换为实际的后端 API 地址
3. **小程序配置**: 需要在微信公众平台配置相关权限和域名
4. **样式适配**: 已适配主流机型，如需特殊适配请修改相应样式

## 技术支持

如有问题，请联系开发团队或查看相关文档：

- [Taro 官方文档](https://taro-docs.jd.com/)
- [NutUI React Taro 文档](https://nutui.jd.com/react/taro/)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/)
