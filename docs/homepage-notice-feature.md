# 首页公告滚动功能实现文档

## 功能概述

实现了首页公告的上下滚动功能，支持从接口动态获取公告内容、联系方式（手机号、微信号）和分享内容。

## 主要功能

### 1. 公告滚动显示
- 使用 NutUI 的 NoticeBar 组件实现公告滚动
- 支持多条公告内容循环滚动
- 自动从接口获取公告数据

### 2. 动态联系方式
- 手机号从接口获取，支持一键拨打
- 微信号从接口获取，支持一键复制
- 分享内容从接口获取，支持自定义分享标题和链接

### 3. 接口集成
- 新增首页配置相关 API 接口
- 支持开发环境模拟数据
- 生产环境调用真实接口

## 技术实现

### API 接口设计

```typescript
// 首页配置相关接口
api.home = {
  // 获取首页配置信息（包含公告、联系方式、分享内容等）
  getConfig: () => Promise<HomeConfig>,
  
  // 获取公告列表
  getNotices: () => Promise<NoticeItem[]>,
  
  // 获取联系方式
  getContactInfo: () => Promise<ContactInfo>
}
```

### 数据结构

```typescript
interface NoticeItem {
  id: number;
  content: string;
  type?: string;
}

interface ContactInfo {
  phone: string;
  wechat: string;
  shareContent: {
    title: string;
    path: string;
    imageUrl?: string;
  };
}
```

### 组件实现

```tsx
// 公告栏组件
<NoticeBar
  text={notices.map(notice => notice.content).join('    ')}
  background="#fff7e6"
  color="#fa8c16"
  leftIcon="volume"
  scrollable
/>
```

## 模拟数据

在开发环境下，系统会使用模拟数据进行测试：

```typescript
export const mockHomeConfig = {
  notices: [
    {
      id: 1,
      content: '📢 如有闲置手机请告知回收人员',
      type: 'info'
    },
    {
      id: 2,
      content: '🎉 新用户注册即享专属优惠，最高可获得额外100元回收金',
      type: 'promotion'
    },
    // ... 更多公告
  ],
  contact: {
    phone: '18210924745',
    wechat: 'baofeng_recycle_2024',
    shareContent: {
      title: '暴风回收 - 专业手机回收平台，高价回收，秒到账',
      path: '/pages/index/index',
      imageUrl: 'https://...'
    }
  }
};
```

## 使用方法

### 1. 开发环境测试
```bash
npm run dev:weapp
```

### 2. 生产环境配置
需要在后端实现以下接口：
- `GET /home/<USER>
- `GET /home/<USER>
- `GET /home/<USER>

### 3. 接口返回格式
```json
{
  "notices": [
    {
      "id": 1,
      "content": "公告内容",
      "type": "info"
    }
  ],
  "contact": {
    "phone": "18210924745",
    "wechat": "baofeng_recycle_2024",
    "shareContent": {
      "title": "分享标题",
      "path": "/pages/index/index",
      "imageUrl": "分享图片URL"
    }
  }
}
```

## 注意事项

1. **环境区分**: 开发环境自动使用模拟数据，生产环境调用真实接口
2. **错误处理**: 接口调用失败时会使用默认数据，确保功能正常
3. **性能优化**: 公告数据在页面加载时获取，避免频繁请求
4. **兼容性**: 使用 NutUI 组件确保小程序平台兼容性

## 后续扩展

1. 支持公告点击跳转功能
2. 支持公告优先级排序
3. 支持公告有效期管理
4. 支持公告阅读状态记录
