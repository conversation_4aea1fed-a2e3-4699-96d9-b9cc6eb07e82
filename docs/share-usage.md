# 分享功能使用指南

## 概述

项目已经集成了完整的微信小程序分享功能，支持分享给微信好友、分享到朋友圈以及复制小程序链接。

## 功能特性

- ✅ 直接调用微信分享API
- ✅ 支持分享给微信好友
- ✅ 支持分享到朋友圈
- ✅ 支持复制小程序链接
- ✅ 统一的分享配置管理
- ✅ 跨页面分享配置
- ✅ 环境适配（微信小程序/其他环境）

## 使用方法

### 1. 导入分享服务

```typescript
import { ShareService } from '../../services'
```

### 2. 页面分享配置

在页面组件中配置分享功能：

```typescript
function MyPage() {
  // 配置分享给好友
  Taro.useShareAppMessage(() => {
    return ShareService.getShareConfig({
      title: '自定义分享标题',
      path: '/pages/index/index',
      imageUrl: '分享图片URL'
    })
  })

  // 配置分享到朋友圈
  Taro.useShareTimeline(() => {
    return {
      title: '朋友圈分享标题',
      imageUrl: '分享图片URL'
    }
  })

  // 其他组件逻辑...
}
```

### 3. 主动分享功能

#### 一键分享（推荐）

```typescript
const handleShare = () => {
  ShareService.oneClickShare({
    title: '暴风回收 - 专业手机回收平台',
    path: '/pages/index/index',
    imageUrl: 'https://example.com/share-image.png',
    link: 'https://www.baofengrecycle.com/miniprogram'
  })
}
```

#### 分享给微信好友

```typescript
const handleShareToFriend = () => {
  ShareService.shareToFriend({
    title: '分享标题',
    path: '/pages/index/index',
    imageUrl: '分享图片URL'
  })
}
```

#### 分享到朋友圈

```typescript
const handleShareToMoment = () => {
  ShareService.shareToMoment({
    title: '朋友圈分享标题',
    path: '/pages/index/index',
    imageUrl: '分享图片URL'
  })
}
```

#### 复制小程序链接

```typescript
const handleCopyLink = async () => {
  await ShareService.copyMiniProgramLink('https://www.baofengrecycle.com/miniprogram')
}
```

### 4. 分享菜单控制

#### 显示分享菜单

```typescript
// 显示所有分享选项
ShareService.showShareMenu()

// 只显示分享给好友
ShareService.showShareMenu({
  showShareItems: ['wechatFriends']
})

// 只显示分享到朋友圈
ShareService.showShareMenu({
  showShareItems: ['wechatMoment']
})
```

#### 隐藏分享菜单

```typescript
ShareService.hideShareMenu()
```

## API 参考

### ShareService.getShareConfig(options?)

获取分享配置对象。

**参数：**
- `options.title?` - 分享标题
- `options.path?` - 分享页面路径
- `options.imageUrl?` - 分享图片URL

**返回：** 分享配置对象

### ShareService.oneClickShare(options?)

一键分享功能，显示分享选项供用户选择。

**参数：**
- `options.title?` - 分享标题
- `options.path?` - 分享页面路径
- `options.imageUrl?` - 分享图片URL
- `options.link?` - 复制链接地址

### ShareService.shareToFriend(options?)

分享给微信好友。

**参数：**
- `options.title?` - 分享标题
- `options.path?` - 分享页面路径
- `options.imageUrl?` - 分享图片URL

### ShareService.shareToMoment(options?)

分享到朋友圈。

**参数：**
- `options.title?` - 分享标题
- `options.path?` - 分享页面路径
- `options.imageUrl?` - 分享图片URL

### ShareService.copyMiniProgramLink(link?)

复制小程序链接到剪贴板。

**参数：**
- `link?` - 要复制的链接地址

### ShareService.showShareMenu(options?)

显示分享菜单。

**参数：**
- `options.withShareTicket?` - 是否使用带 shareTicket 的转发
- `options.showShareItems?` - 显示的分享选项数组

### ShareService.hideShareMenu()

隐藏分享菜单。

## 配置说明

### 默认分享配置

```typescript
{
  title: '暴风回收 - 专业手机回收平台',
  path: '/pages/index/index',
  imageUrl: 'https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/4138097748889987.png'
}
```

### 全局分享配置

在 `src/app.config.ts` 中配置：

```typescript
{
  shareAppMessage: {
    title: '暴风回收 - 专业手机回收平台',
    path: '/pages/index/index',
    imageUrl: ''
  }
}
```

## 注意事项

1. **环境适配**：分享功能会自动检测运行环境，在非微信小程序环境中会显示相应提示。

2. **分享图片**：建议使用 HTTPS 链接的图片，尺寸建议为 5:4 比例。

3. **分享路径**：确保分享的页面路径是有效的小程序页面路径。

4. **权限配置**：确保在 `app.config.ts` 中正确配置了分享相关权限。

5. **用户体验**：分享功能会引导用户点击右上角分享按钮，这是微信小程序的标准分享流程。

## 示例代码

完整的页面分享实现示例：

```typescript
import React from 'react'
import { View, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { ShareService } from '../../services'

function ShareExample() {
  // 配置页面分享
  Taro.useShareAppMessage(() => {
    return ShareService.getShareConfig({
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index'
    })
  })

  Taro.useShareTimeline(() => {
    return {
      title: '暴风回收 - 让闲置物品变现更简单！',
      imageUrl: 'https://example.com/share-image.png'
    }
  })

  // 分享按钮点击处理
  const handleShare = () => {
    ShareService.oneClickShare({
      title: '暴风回收 - 专业手机回收平台',
      path: '/pages/index/index',
      imageUrl: 'https://example.com/share-image.png',
      link: 'https://www.baofengrecycle.com/miniprogram'
    })
  }

  return (
    <View>
      <Button onClick={handleShare}>
        一键分享
      </Button>
    </View>
  )
}

export default ShareExample
```
