# 微信登录功能说明

## 功能概述

在进入首页时，小程序会自动调用 `wx.login` 接口进行微信登录，获取用户的临时登录凭证 code，并可选择性地将其发送到后端服务器换取用户的 openid 和 session_key。

## 实现方式

### 1. 用户服务类 (`src/services/user.ts`)

创建了一个专门的用户服务类 `UserService`，提供以下功能：

- **自动登录**: `autoLogin()` - 检查本地存储，如果没有用户信息则执行微信登录
- **微信登录**: `wxLogin()` - 调用 wx.login 获取 code，并发送到后端
- **用户信息管理**: 保存、获取、清除本地用户信息
- **登录状态检查**: 检查用户是否已登录

### 2. API 接口 (`src/services/api.ts`)

添加了微信登录的 API 接口：

```typescript
// 微信登录
wxLogin: (data: { code: string }) => request({
  url: '/user/wx-login',
  method: 'POST',
  data
})
```

### 3. 首页集成 (`src/pages/index/index.tsx`)

在首页的 `useEffect` 中调用自动登录：

```typescript
useEffect(() => {
  // 使用 UserService 进行自动登录
  UserService.autoLogin()
    .then((userData) => {
      console.log('登录成功，用户信息:', userData);
      setUserInfo(userData);
      // 登录成功后获取首页配置
      fetchHomeConfig();
    })
    .catch((error) => {
      console.error('登录失败:', error);
      // 即使登录失败也要加载首页数据
      fetchHomeConfig();
    });
}, []);
```

## 登录流程

1. **进入首页** → 触发 `useEffect`
2. **检查本地存储** → 如果有用户信息，直接使用
3. **执行微信登录** → 调用 `wx.login` 获取 code
4. **发送到后端** → 将 code 发送到 `/user/wx-login` 接口
5. **保存用户信息** → 将返回的用户信息保存到本地存储和状态
6. **加载首页数据** → 无论登录成功与否，都会加载首页内容

## 错误处理

- **微信登录失败**: 显示错误提示，但仍会加载首页内容
- **后端接口失败**: 保存基本的登录信息（code + 时间戳），继续使用
- **本地存储失败**: 记录错误日志，不影响主要功能

## 开发环境提示

在开发环境下，首页顶部会显示用户登录状态：

```typescript
{process.env.NODE_ENV === 'development' && userInfo && (
  <View style={{ 
    padding: '5px 10px', 
    backgroundColor: '#e6f7ff', 
    fontSize: '12px', 
    color: '#1890ff',
    textAlign: 'center'
  }}>
    用户已登录 {userInfo.openid ? `(OpenID: ${userInfo.openid.substring(0, 8)}...)` : '(本地登录)'}
  </View>
)}
```

## 后端接口要求

后端需要提供 `/user/wx-login` 接口，接收参数：

```json
{
  "code": "微信登录返回的临时凭证"
}
```

返回格式建议：

```json
{
  "openid": "用户的openid",
  "session_key": "会话密钥",
  "unionid": "用户的unionid（可选）",
  "nickName": "用户昵称（可选）",
  "avatarUrl": "用户头像（可选）"
}
```

## 使用说明

1. **域名配置**: 确保在微信公众平台配置了服务器域名，或在开发时关闭域名校验
2. **AppID配置**: 在 `project.config.json` 中配置正确的小程序 AppID
3. **后端接口**: 确保后端提供了对应的微信登录接口

## 注意事项

- 微信登录的 code 只能使用一次，且有时效性（5分钟）
- 本地存储的用户信息在小程序删除时会被清除
- 在正式环境中，建议定期刷新用户的登录状态
- 用户信息的获取需要用户授权，请遵循微信小程序的相关规范
